/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { EmailSummary } from './EmailSummary';
import {
    EmailSummaryFromJSON,
    EmailSummaryFromJSONTyped,
    EmailSummaryToJSON,
} from './EmailSummary';

/**
 * 
 * @export
 * @interface EmailListResponse
 */
export interface EmailListResponse {
    /**
     * 
     * @type {Array<EmailSummary>}
     * @memberof EmailListResponse
     */
    emails: Array<EmailSummary>;
    /**
     * 
     * @type {string}
     * @memberof EmailListResponse
     */
    nextPageToken?: string | null;
}

/**
 * Check if a given object implements the EmailListResponse interface.
 */
export function instanceOfEmailListResponse(value: object): value is EmailListResponse {
    if (!('emails' in value) || value['emails'] === undefined) return false;
    return true;
}

export function EmailListResponseFromJSON(json: any): EmailListResponse {
    return EmailListResponseFromJSONTyped(json, false);
}

export function EmailListResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): EmailListResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'emails': ((json['emails'] as Array<any>).map(EmailSummaryFromJSON)),
        'nextPageToken': json['next_page_token'] == null ? undefined : json['next_page_token'],
    };
}

export function EmailListResponseToJSON(value?: EmailListResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'emails': ((value['emails'] as Array<any>).map(EmailSummaryToJSON)),
        'next_page_token': value['nextPageToken'],
    };
}

