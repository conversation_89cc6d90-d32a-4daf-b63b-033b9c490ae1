/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface ActionItem
 */
export interface ActionItem {
    /**
     * 
     * @type {string}
     * @memberof ActionItem
     */
    uuid: string;
    /**
     * 
     * @type {string}
     * @memberof ActionItem
     */
    content: string;
    /**
     * 
     * @type {string}
     * @memberof ActionItem
     */
    status: string;
    /**
     * 
     * @type {Date}
     * @memberof ActionItem
     */
    dueDate?: Date | null;
    /**
     * 
     * @type {string}
     * @memberof ActionItem
     */
    assignee?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ActionItem
     */
    assigneeType?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ActionItem
     */
    workflowTypeName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ActionItem
     */
    crmAssignee?: string | null;
}

/**
 * Check if a given object implements the ActionItem interface.
 */
export function instanceOfActionItem(value: object): value is ActionItem {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('content' in value) || value['content'] === undefined) return false;
    if (!('status' in value) || value['status'] === undefined) return false;
    return true;
}

export function ActionItemFromJSON(json: any): ActionItem {
    return ActionItemFromJSONTyped(json, false);
}

export function ActionItemFromJSONTyped(json: any, ignoreDiscriminator: boolean): ActionItem {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'content': json['content'],
        'status': json['status'],
        'dueDate': json['due_date'] == null ? undefined : (new Date(json['due_date'])),
        'assignee': json['assignee'] == null ? undefined : json['assignee'],
        'assigneeType': json['assignee_type'] == null ? undefined : json['assignee_type'],
        'workflowTypeName': json['workflow_type_name'] == null ? undefined : json['workflow_type_name'],
        'crmAssignee': json['crm_assignee'] == null ? undefined : json['crm_assignee'],
    };
}

export function ActionItemToJSON(value?: ActionItem | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'content': value['content'],
        'status': value['status'],
        'due_date': value['dueDate'] == null ? undefined : ((value['dueDate'] as any).toISOString()),
        'assignee': value['assignee'],
        'assignee_type': value['assigneeType'],
        'workflow_type_name': value['workflowTypeName'],
        'crm_assignee': value['crmAssignee'],
    };
}

