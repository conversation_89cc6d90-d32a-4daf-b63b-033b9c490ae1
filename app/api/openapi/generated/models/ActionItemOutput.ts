/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { AssigneeType } from './AssigneeType';
import {
    AssigneeTypeFromJSON,
    AssigneeTypeFromJSONTyped,
    AssigneeTypeToJSON,
} from './AssigneeType';
import type { ApiRoutersNoteModelsClient } from './ApiRoutersNoteModelsClient';
import {
    ApiRoutersNoteModelsClientFromJSON,
    ApiRoutersNoteModelsClientFromJSONTyped,
    ApiRoutersNoteModelsClientToJSON,
} from './ApiRoutersNoteModelsClient';
import type { CRMUser } from './CRMUser';
import {
    CRMUserFromJSON,
    CRMUser<PERSON>romJSONTyped,
    CRMUserToJSON,
} from './CRMUser';

/**
 * 
 * @export
 * @interface ActionItemOutput
 */
export interface ActionItemOutput {
    /**
     * 
     * @type {string}
     * @memberof ActionItemOutput
     */
    uuid: string;
    /**
     * 
     * @type {string}
     * @memberof ActionItemOutput
     */
    content: string;
    /**
     * 
     * @type {string}
     * @memberof ActionItemOutput
     */
    status: string;
    /**
     * 
     * @type {Date}
     * @memberof ActionItemOutput
     */
    dueDate?: Date | null;
    /**
     * 
     * @type {ApiRoutersNoteModelsClient}
     * @memberof ActionItemOutput
     */
    assignee?: ApiRoutersNoteModelsClient | null;
    /**
     * 
     * @type {Array<ApiRoutersNoteModelsClient>}
     * @memberof ActionItemOutput
     */
    assignees?: Array<ApiRoutersNoteModelsClient> | null;
    /**
     * 
     * @type {string}
     * @memberof ActionItemOutput
     */
    workflowTypeName?: string | null;
    /**
     * 
     * @type {CRMUser}
     * @memberof ActionItemOutput
     */
    crmAssignee?: CRMUser | null;
    /**
     * 
     * @type {AssigneeType}
     * @memberof ActionItemOutput
     */
    assigneeType?: AssigneeType | null;
}



/**
 * Check if a given object implements the ActionItemOutput interface.
 */
export function instanceOfActionItemOutput(value: object): value is ActionItemOutput {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('content' in value) || value['content'] === undefined) return false;
    if (!('status' in value) || value['status'] === undefined) return false;
    return true;
}

export function ActionItemOutputFromJSON(json: any): ActionItemOutput {
    return ActionItemOutputFromJSONTyped(json, false);
}

export function ActionItemOutputFromJSONTyped(json: any, ignoreDiscriminator: boolean): ActionItemOutput {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'content': json['content'],
        'status': json['status'],
        'dueDate': json['due_date'] == null ? undefined : (new Date(json['due_date'])),
        'assignee': json['assignee'] == null ? undefined : ApiRoutersNoteModelsClientFromJSON(json['assignee']),
        'assignees': json['assignees'] == null ? undefined : ((json['assignees'] as Array<any>).map(ApiRoutersNoteModelsClientFromJSON)),
        'workflowTypeName': json['workflow_type_name'] == null ? undefined : json['workflow_type_name'],
        'crmAssignee': json['crm_assignee'] == null ? undefined : CRMUserFromJSON(json['crm_assignee']),
        'assigneeType': json['assignee_type'] == null ? undefined : AssigneeTypeFromJSON(json['assignee_type']),
    };
}

export function ActionItemOutputToJSON(value?: ActionItemOutput | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'content': value['content'],
        'status': value['status'],
        'due_date': value['dueDate'] == null ? undefined : ((value['dueDate'] as any).toISOString()),
        'assignee': ApiRoutersNoteModelsClientToJSON(value['assignee']),
        'assignees': value['assignees'] == null ? undefined : ((value['assignees'] as Array<any>).map(ApiRoutersNoteModelsClientToJSON)),
        'workflow_type_name': value['workflowTypeName'],
        'crm_assignee': CRMUserToJSON(value['crmAssignee']),
        'assignee_type': AssigneeTypeToJSON(value['assigneeType']),
    };
}

