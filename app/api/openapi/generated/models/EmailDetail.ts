/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface EmailDetail
 */
export interface EmailDetail {
    /**
     * Email provider (gmail or microsoft)
     * @type {string}
     * @memberof EmailDetail
     */
    provider: string;
    /**
     * Unique message ID
     * @type {string}
     * @memberof EmailDetail
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof EmailDetail
     */
    threadId?: string | null;
    /**
     * Email subject
     * @type {string}
     * @memberof EmailDetail
     */
    subject: string;
    /**
     * Sender email address
     * @type {string}
     * @memberof EmailDetail
     */
    sender: string;
    /**
     * Primary recipients (To)
     * @type {Array<string>}
     * @memberof EmailDetail
     */
    recipients?: Array<string>;
    /**
     * CC recipients
     * @type {Array<string>}
     * @memberof EmailDetail
     */
    ccRecipients?: Array<string>;
    /**
     * BCC recipients
     * @type {Array<string>}
     * @memberof EmailDetail
     */
    bccRecipients?: Array<string>;
    /**
     * 
     * @type {string}
     * @memberof EmailDetail
     */
    bodyText?: string | null;
    /**
     * 
     * @type {string}
     * @memberof EmailDetail
     */
    bodyHtml?: string | null;
    /**
     * Short preview of email content
     * @type {string}
     * @memberof EmailDetail
     */
    snippet: string;
    /**
     * Whether the email has been read
     * @type {boolean}
     * @memberof EmailDetail
     */
    isRead: boolean;
    /**
     * Time the email was received
     * @type {Date}
     * @memberof EmailDetail
     */
    receivedTime: Date;
    /**
     * 
     * @type {Date}
     * @memberof EmailDetail
     */
    sentTime?: Date | null;
    /**
     * Whether email has attachments
     * @type {boolean}
     * @memberof EmailDetail
     */
    hasAttachments?: boolean;
    /**
     * Names of attachments
     * @type {Array<string>}
     * @memberof EmailDetail
     */
    attachmentNames?: Array<string>;
    /**
     * Email labels/categories
     * @type {Array<string>}
     * @memberof EmailDetail
     */
    labels?: Array<string>;
}

/**
 * Check if a given object implements the EmailDetail interface.
 */
export function instanceOfEmailDetail(value: object): value is EmailDetail {
    if (!('provider' in value) || value['provider'] === undefined) return false;
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('subject' in value) || value['subject'] === undefined) return false;
    if (!('sender' in value) || value['sender'] === undefined) return false;
    if (!('snippet' in value) || value['snippet'] === undefined) return false;
    if (!('isRead' in value) || value['isRead'] === undefined) return false;
    if (!('receivedTime' in value) || value['receivedTime'] === undefined) return false;
    return true;
}

export function EmailDetailFromJSON(json: any): EmailDetail {
    return EmailDetailFromJSONTyped(json, false);
}

export function EmailDetailFromJSONTyped(json: any, ignoreDiscriminator: boolean): EmailDetail {
    if (json == null) {
        return json;
    }
    return {
        
        'provider': json['provider'],
        'id': json['id'],
        'threadId': json['thread_id'] == null ? undefined : json['thread_id'],
        'subject': json['subject'],
        'sender': json['sender'],
        'recipients': json['recipients'] == null ? undefined : json['recipients'],
        'ccRecipients': json['cc_recipients'] == null ? undefined : json['cc_recipients'],
        'bccRecipients': json['bcc_recipients'] == null ? undefined : json['bcc_recipients'],
        'bodyText': json['body_text'] == null ? undefined : json['body_text'],
        'bodyHtml': json['body_html'] == null ? undefined : json['body_html'],
        'snippet': json['snippet'],
        'isRead': json['is_read'],
        'receivedTime': (new Date(json['received_time'])),
        'sentTime': json['sent_time'] == null ? undefined : (new Date(json['sent_time'])),
        'hasAttachments': json['has_attachments'] == null ? undefined : json['has_attachments'],
        'attachmentNames': json['attachment_names'] == null ? undefined : json['attachment_names'],
        'labels': json['labels'] == null ? undefined : json['labels'],
    };
}

export function EmailDetailToJSON(value?: EmailDetail | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'provider': value['provider'],
        'id': value['id'],
        'thread_id': value['threadId'],
        'subject': value['subject'],
        'sender': value['sender'],
        'recipients': value['recipients'],
        'cc_recipients': value['ccRecipients'],
        'bcc_recipients': value['bccRecipients'],
        'body_text': value['bodyText'],
        'body_html': value['bodyHtml'],
        'snippet': value['snippet'],
        'is_read': value['isRead'],
        'received_time': ((value['receivedTime']).toISOString()),
        'sent_time': value['sentTime'] == null ? undefined : ((value['sentTime'] as any).toISOString()),
        'has_attachments': value['hasAttachments'],
        'attachment_names': value['attachmentNames'],
        'labels': value['labels'],
    };
}

