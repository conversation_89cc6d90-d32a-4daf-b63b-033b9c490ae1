/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { ClientType } from './ClientType';
import {
    ClientTypeFromJSON,
    ClientTypeFromJSONTyped,
    ClientTypeToJSON,
} from './ClientType';
import type { ClientRecapOutput } from './ClientRecapOutput';
import {
    ClientRecapOutputFromJSON,
    ClientRecapOutputFromJSONTyped,
    ClientRecapOutputToJSON,
} from './ClientRecapOutput';
import type { ListNotesResponse } from './ListNotesResponse';
import {
    ListNotesResponseFromJSON,
    ListNotesResponseFromJSONTyped,
    ListNotesResponseToJSON,
} from './ListNotesResponse';
import type { ClientRecapStatus } from './ClientRecapStatus';
import {
    ClientRecapStatusFromJSON,
    ClientRecapStatusFromJSONTyped,
    ClientRecapStatusToJSON,
} from './ClientRecapStatus';
import type { TaskResponse } from './TaskResponse';
import {
    TaskResponseFromJSON,
    TaskResponseFromJSONTyped,
    TaskResponseToJSON,
} from './TaskResponse';

/**
 * 
 * @export
 * @interface ApiRoutersClientClientResponse
 */
export interface ApiRoutersClientClientResponse {
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersClientClientResponse
     */
    name: string;
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersClientClientResponse
     */
    email: string | null;
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersClientClientResponse
     */
    firstName: string | null;
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersClientClientResponse
     */
    lastName: string | null;
    /**
     * 
     * @type {Date}
     * @memberof ApiRoutersClientClientResponse
     */
    dateOfBirth: Date | null;
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersClientClientResponse
     */
    jobTitle: string | null;
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersClientClientResponse
     */
    phoneNumber?: string;
    /**
     * 
     * @type {ClientType}
     * @memberof ApiRoutersClientClientResponse
     */
    clientType: ClientType;
    /**
     * 
     * @type {Date}
     * @memberof ApiRoutersClientClientResponse
     */
    onboardingDate: Date | null;
    /**
     * 
     * @type {ClientRecapStatus}
     * @memberof ApiRoutersClientClientResponse
     */
    recapStatus: ClientRecapStatus | null;
    /**
     * 
     * @type {ClientRecapOutput}
     * @memberof ApiRoutersClientClientResponse
     */
    recap: ClientRecapOutput | null;
    /**
     * 
     * @type {Array<ListNotesResponse>}
     * @memberof ApiRoutersClientClientResponse
     */
    notes: Array<ListNotesResponse>;
    /**
     * 
     * @type {{ [key: string]: any; }}
     * @memberof ApiRoutersClientClientResponse
     */
    basicInfo: { [key: string]: any; };
    /**
     * 
     * @type {Array<TaskResponse>}
     * @memberof ApiRoutersClientClientResponse
     */
    actionItems: Array<TaskResponse> | null;
}



/**
 * Check if a given object implements the ApiRoutersClientClientResponse interface.
 */
export function instanceOfApiRoutersClientClientResponse(value: object): value is ApiRoutersClientClientResponse {
    if (!('name' in value) || value['name'] === undefined) return false;
    if (!('email' in value) || value['email'] === undefined) return false;
    if (!('firstName' in value) || value['firstName'] === undefined) return false;
    if (!('lastName' in value) || value['lastName'] === undefined) return false;
    if (!('dateOfBirth' in value) || value['dateOfBirth'] === undefined) return false;
    if (!('jobTitle' in value) || value['jobTitle'] === undefined) return false;
    if (!('clientType' in value) || value['clientType'] === undefined) return false;
    if (!('onboardingDate' in value) || value['onboardingDate'] === undefined) return false;
    if (!('recapStatus' in value) || value['recapStatus'] === undefined) return false;
    if (!('recap' in value) || value['recap'] === undefined) return false;
    if (!('notes' in value) || value['notes'] === undefined) return false;
    if (!('basicInfo' in value) || value['basicInfo'] === undefined) return false;
    if (!('actionItems' in value) || value['actionItems'] === undefined) return false;
    return true;
}

export function ApiRoutersClientClientResponseFromJSON(json: any): ApiRoutersClientClientResponse {
    return ApiRoutersClientClientResponseFromJSONTyped(json, false);
}

export function ApiRoutersClientClientResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): ApiRoutersClientClientResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'name': json['name'],
        'email': json['email'],
        'firstName': json['first_name'],
        'lastName': json['last_name'],
        'dateOfBirth': (json['date_of_birth'] == null ? null : new Date(json['date_of_birth'])),
        'jobTitle': json['job_title'],
        'phoneNumber': json['phone_number'] == null ? undefined : json['phone_number'],
        'clientType': ClientTypeFromJSON(json['client_type']),
        'onboardingDate': (json['onboarding_date'] == null ? null : new Date(json['onboarding_date'])),
        'recapStatus': ClientRecapStatusFromJSON(json['recap_status']),
        'recap': ClientRecapOutputFromJSON(json['recap']),
        'notes': ((json['notes'] as Array<any>).map(ListNotesResponseFromJSON)),
        'basicInfo': json['basic_info'],
        'actionItems': (json['action_items'] == null ? null : (json['action_items'] as Array<any>).map(TaskResponseFromJSON)),
    };
}

export function ApiRoutersClientClientResponseToJSON(value?: ApiRoutersClientClientResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'name': value['name'],
        'email': value['email'],
        'first_name': value['firstName'],
        'last_name': value['lastName'],
        'date_of_birth': (value['dateOfBirth'] == null ? null : (value['dateOfBirth'] as any).toISOString().substring(0,10)),
        'job_title': value['jobTitle'],
        'phone_number': value['phoneNumber'],
        'client_type': ClientTypeToJSON(value['clientType']),
        'onboarding_date': (value['onboardingDate'] == null ? null : (value['onboardingDate'] as any).toISOString().substring(0,10)),
        'recap_status': ClientRecapStatusToJSON(value['recapStatus']),
        'recap': ClientRecapOutputToJSON(value['recap']),
        'notes': ((value['notes'] as Array<any>).map(ListNotesResponseToJSON)),
        'basic_info': value['basicInfo'],
        'action_items': (value['actionItems'] == null ? null : (value['actionItems'] as Array<any>).map(TaskResponseToJSON)),
    };
}

