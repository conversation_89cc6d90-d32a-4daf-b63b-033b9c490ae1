/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface EmailSummary
 */
export interface EmailSummary {
    /**
     * Email provider (gmail or microsoft)
     * @type {string}
     * @memberof EmailSummary
     */
    provider: string;
    /**
     * Unique message ID
     * @type {string}
     * @memberof EmailSummary
     */
    id: string;
    /**
     * Email subject
     * @type {string}
     * @memberof EmailSummary
     */
    subject: string;
    /**
     * Sender email address
     * @type {string}
     * @memberof EmailSummary
     */
    sender: string;
    /**
     * Primary recipients
     * @type {Array<string>}
     * @memberof EmailSummary
     */
    recipients?: Array<string>;
    /**
     * Short preview of email content
     * @type {string}
     * @memberof EmailSummary
     */
    snippet: string;
    /**
     * Whether the email has been read
     * @type {boolean}
     * @memberof EmailSummary
     */
    isRead: boolean;
    /**
     * Time the email was received
     * @type {Date}
     * @memberof EmailSummary
     */
    receivedTime: Date;
    /**
     * Whether email has attachments
     * @type {boolean}
     * @memberof EmailSummary
     */
    hasAttachments?: boolean;
    /**
     * Email labels/categories
     * @type {Array<string>}
     * @memberof EmailSummary
     */
    labels?: Array<string>;
}

/**
 * Check if a given object implements the EmailSummary interface.
 */
export function instanceOfEmailSummary(value: object): value is EmailSummary {
    if (!('provider' in value) || value['provider'] === undefined) return false;
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('subject' in value) || value['subject'] === undefined) return false;
    if (!('sender' in value) || value['sender'] === undefined) return false;
    if (!('snippet' in value) || value['snippet'] === undefined) return false;
    if (!('isRead' in value) || value['isRead'] === undefined) return false;
    if (!('receivedTime' in value) || value['receivedTime'] === undefined) return false;
    return true;
}

export function EmailSummaryFromJSON(json: any): EmailSummary {
    return EmailSummaryFromJSONTyped(json, false);
}

export function EmailSummaryFromJSONTyped(json: any, ignoreDiscriminator: boolean): EmailSummary {
    if (json == null) {
        return json;
    }
    return {
        
        'provider': json['provider'],
        'id': json['id'],
        'subject': json['subject'],
        'sender': json['sender'],
        'recipients': json['recipients'] == null ? undefined : json['recipients'],
        'snippet': json['snippet'],
        'isRead': json['is_read'],
        'receivedTime': (new Date(json['received_time'])),
        'hasAttachments': json['has_attachments'] == null ? undefined : json['has_attachments'],
        'labels': json['labels'] == null ? undefined : json['labels'],
    };
}

export function EmailSummaryToJSON(value?: EmailSummary | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'provider': value['provider'],
        'id': value['id'],
        'subject': value['subject'],
        'sender': value['sender'],
        'recipients': value['recipients'],
        'snippet': value['snippet'],
        'is_read': value['isRead'],
        'received_time': ((value['receivedTime']).toISOString()),
        'has_attachments': value['hasAttachments'],
        'labels': value['labels'],
    };
}

