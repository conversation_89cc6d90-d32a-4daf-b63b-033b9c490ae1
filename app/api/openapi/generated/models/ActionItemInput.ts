/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { AssigneeType } from './AssigneeType';
import {
    AssigneeTypeFromJSON,
    AssigneeTypeFromJSONTyped,
    AssigneeTypeToJSON,
} from './AssigneeType';
import type { ClientInput } from './ClientInput';
import {
    ClientInputFromJSON,
    ClientInputFromJSONTyped,
    ClientInputToJSON,
} from './ClientInput';
import type { CRMUser } from './CRMUser';
import {
    CRMUserFromJSON,
    CRMUserFromJSONTyped,
    CRMUserToJSON,
} from './CRMUser';

/**
 * 
 * @export
 * @interface ActionItemInput
 */
export interface ActionItemInput {
    /**
     * 
     * @type {string}
     * @memberof ActionItemInput
     */
    uuid: string;
    /**
     * 
     * @type {string}
     * @memberof ActionItemInput
     */
    content: string;
    /**
     * 
     * @type {string}
     * @memberof ActionItemInput
     */
    status: string;
    /**
     * 
     * @type {Date}
     * @memberof ActionItemInput
     */
    dueDate?: Date | null;
    /**
     * 
     * @type {ClientInput}
     * @memberof ActionItemInput
     */
    assignee?: ClientInput | null;
    /**
     * 
     * @type {Array<ClientInput>}
     * @memberof ActionItemInput
     */
    assignees?: Array<ClientInput> | null;
    /**
     * 
     * @type {string}
     * @memberof ActionItemInput
     */
    workflowTypeName?: string | null;
    /**
     * 
     * @type {CRMUser}
     * @memberof ActionItemInput
     */
    crmAssignee?: CRMUser | null;
    /**
     * 
     * @type {AssigneeType}
     * @memberof ActionItemInput
     */
    assigneeType?: AssigneeType | null;
}



/**
 * Check if a given object implements the ActionItemInput interface.
 */
export function instanceOfActionItemInput(value: object): value is ActionItemInput {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('content' in value) || value['content'] === undefined) return false;
    if (!('status' in value) || value['status'] === undefined) return false;
    return true;
}

export function ActionItemInputFromJSON(json: any): ActionItemInput {
    return ActionItemInputFromJSONTyped(json, false);
}

export function ActionItemInputFromJSONTyped(json: any, ignoreDiscriminator: boolean): ActionItemInput {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'content': json['content'],
        'status': json['status'],
        'dueDate': json['due_date'] == null ? undefined : (new Date(json['due_date'])),
        'assignee': json['assignee'] == null ? undefined : ClientInputFromJSON(json['assignee']),
        'assignees': json['assignees'] == null ? undefined : ((json['assignees'] as Array<any>).map(ClientInputFromJSON)),
        'workflowTypeName': json['workflow_type_name'] == null ? undefined : json['workflow_type_name'],
        'crmAssignee': json['crm_assignee'] == null ? undefined : CRMUserFromJSON(json['crm_assignee']),
        'assigneeType': json['assignee_type'] == null ? undefined : AssigneeTypeFromJSON(json['assignee_type']),
    };
}

export function ActionItemInputToJSON(value?: ActionItemInput | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'content': value['content'],
        'status': value['status'],
        'due_date': value['dueDate'] == null ? undefined : ((value['dueDate'] as any).toISOString()),
        'assignee': ClientInputToJSON(value['assignee']),
        'assignees': value['assignees'] == null ? undefined : ((value['assignees'] as Array<any>).map(ClientInputToJSON)),
        'workflow_type_name': value['workflowTypeName'],
        'crm_assignee': CRMUserToJSON(value['crmAssignee']),
        'assignee_type': AssigneeTypeToJSON(value['assigneeType']),
    };
}

