/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * Request model for the ask_anything endpoint.
 * @export
 * @interface AskAnythingRequest
 */
export interface AskAnythingRequest {
    /**
     * 
     * @type {string}
     * @memberof AskAnythingRequest
     */
    query: string;
    /**
     * 
     * @type {string}
     * @memberof AskAnythingRequest
     */
    noteId?: string | null;
    /**
     * 
     * @type {string}
     * @memberof AskAnythingRequest
     */
    clientId?: string | null;
}

/**
 * Check if a given object implements the AskAnythingRequest interface.
 */
export function instanceOfAskAnythingRequest(value: object): value is AskAnythingRequest {
    if (!('query' in value) || value['query'] === undefined) return false;
    return true;
}

export function AskAnythingRequestFromJSON(json: any): AskAnythingRequest {
    return AskAnythingRequestFromJSONTyped(json, false);
}

export function AskAnythingRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): AskAnythingRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'query': json['query'],
        'noteId': json['note_id'] == null ? undefined : json['note_id'],
        'clientId': json['client_id'] == null ? undefined : json['client_id'],
    };
}

export function AskAnythingRequestToJSON(value?: AskAnythingRequest | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'query': value['query'],
        'note_id': value['noteId'],
        'client_id': value['clientId'],
    };
}

