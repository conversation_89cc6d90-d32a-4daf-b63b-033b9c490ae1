/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  EmailDetail,
  EmailListResponse,
  HTTPValidationError,
} from '../models/index';
import {
    EmailDetailFromJSON,
    EmailDetailToJSON,
    EmailListResponseFromJSON,
    EmailListResponseToJSON,
    HTTPValidationErrorFromJSON,
    HTTPValidationErrorToJSON,
} from '../models/index';

export interface EmailGetEmailRequest {
    provider: EmailGetEmailProviderEnum;
    messageId: string;
}

export interface EmailListEmailsRequest {
    q?: string | null;
    sender?: string | null;
    subject?: string | null;
    unreadOnly?: boolean;
    labels?: Array<string> | null;
    startDate?: Date | null;
    endDate?: Date | null;
    cursor?: string | null;
    pageSize?: number;
}

/**
 * 
 */
export class EmailApi extends runtime.BaseAPI {

    /**
     * Fetches complete email details including body content for a specific message ID. This endpoint should be called after list_emails to get full content when a user selects an email to view. The provider parameter ensures we fetch from the correct email service. Returns both plain text and HTML body if available.
     * Get full email details by provider and message ID
     */
    async emailGetEmailRaw(requestParameters: EmailGetEmailRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<EmailDetail>> {
        if (requestParameters['provider'] == null) {
            throw new runtime.RequiredError(
                'provider',
                'Required parameter "provider" was null or undefined when calling emailGetEmail().'
            );
        }

        if (requestParameters['messageId'] == null) {
            throw new runtime.RequiredError(
                'messageId',
                'Required parameter "messageId" was null or undefined when calling emailGetEmail().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/email/emails/{provider}/{message_id}`.replace(`{${"provider"}}`, encodeURIComponent(String(requestParameters['provider']))).replace(`{${"message_id"}}`, encodeURIComponent(String(requestParameters['messageId']))),
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => EmailDetailFromJSON(jsonValue));
    }

    /**
     * Fetches complete email details including body content for a specific message ID. This endpoint should be called after list_emails to get full content when a user selects an email to view. The provider parameter ensures we fetch from the correct email service. Returns both plain text and HTML body if available.
     * Get full email details by provider and message ID
     */
    async emailGetEmail(requestParameters: EmailGetEmailRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<EmailDetail> {
        const response = await this.emailGetEmailRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Fetches a paginated list of email summaries from connected email providers (Gmail/Outlook). Returns lightweight metadata for quick loading. Use the returned message IDs with the GET /emails/{provider}/{message_id} endpoint to fetch full message details including body content.
     * List email messages with pagination
     */
    async emailListEmailsRaw(requestParameters: EmailListEmailsRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<EmailListResponse>> {
        const queryParameters: any = {};

        if (requestParameters['q'] != null) {
            queryParameters['q'] = requestParameters['q'];
        }

        if (requestParameters['sender'] != null) {
            queryParameters['sender'] = requestParameters['sender'];
        }

        if (requestParameters['subject'] != null) {
            queryParameters['subject'] = requestParameters['subject'];
        }

        if (requestParameters['unreadOnly'] != null) {
            queryParameters['unread_only'] = requestParameters['unreadOnly'];
        }

        if (requestParameters['labels'] != null) {
            queryParameters['labels'] = requestParameters['labels'];
        }

        if (requestParameters['startDate'] != null) {
            queryParameters['start_date'] = (requestParameters['startDate'] as any).toISOString();
        }

        if (requestParameters['endDate'] != null) {
            queryParameters['end_date'] = (requestParameters['endDate'] as any).toISOString();
        }

        if (requestParameters['cursor'] != null) {
            queryParameters['cursor'] = requestParameters['cursor'];
        }

        if (requestParameters['pageSize'] != null) {
            queryParameters['page_size'] = requestParameters['pageSize'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/email/emails`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => EmailListResponseFromJSON(jsonValue));
    }

    /**
     * Fetches a paginated list of email summaries from connected email providers (Gmail/Outlook). Returns lightweight metadata for quick loading. Use the returned message IDs with the GET /emails/{provider}/{message_id} endpoint to fetch full message details including body content.
     * List email messages with pagination
     */
    async emailListEmails(requestParameters: EmailListEmailsRequest = {}, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<EmailListResponse> {
        const response = await this.emailListEmailsRaw(requestParameters, initOverrides);
        return await response.value();
    }

}

/**
 * @export
 */
export const EmailGetEmailProviderEnum = {
    Gmail: 'gmail',
    Microsoft: 'microsoft'
} as const;
export type EmailGetEmailProviderEnum = typeof EmailGetEmailProviderEnum[keyof typeof EmailGetEmailProviderEnum];
