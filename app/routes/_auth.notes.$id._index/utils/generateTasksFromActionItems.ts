import {
  ActionItemOutput,
  AssigneeType,
  TaskResponse,
} from "~/api/openapi/generated";

export default function generateTasksFromActionItems(
  actionItems: ActionItemOutput[],
  noteUuid: string
): TaskResponse[] {
  return actionItems.map(
    ({
      uuid,
      content,
      dueDate,
      status,
      assignee,
      assignees,
      assigneeType,
    }) => ({
      uuid: uuid,
      title: content,
      dueDate: dueDate,
      completed: status === "complete",
      assignee: {
        uuid: assignee?.uuid ?? "",
        name: assignee?.name ?? "",
      },
      assignees: (assignees ?? []).map(({ uuid, name }) => ({
        uuid,
        name: name ?? "",
      })),
      assigneeType: assigneeType as AssigneeType,
      parentNoteUuid: noteUuid,
      created: new Date(),
      modified: new Date(),
      owner: null,
    })
  );
}
