import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, it, expect, vi, beforeEach } from "vitest";
import CrmClientsDropdown from "./CrmClientsDropdown";
import {
  ClientListResponse,
  ClientType,
  CrmApi,
} from "~/api/openapi/generated";

// Mock the VirtualizedCombobox to avoid complex virtualization testing
vi.mock("~/@ui/VirtualizedCombobox", () => ({
  VirtualizedCombobox: ({
    options,
    placeholder,
    leftIcon,
    onChange,
    disabled,
    selectedObject,
    loadOptions,
    itemSize,
    maxHeightPx,
  }: any) => {
    return (
      <div data-testid="virtualized-combobox">
        <button
          data-testid="combobox-trigger"
          disabled={disabled}
          onClick={() => loadOptions?.("")}
        >
          {selectedObject?.label || placeholder || "Select client"}
        </button>
        {leftIcon && <div data-testid="left-icon">{leftIcon}</div>}
        <div data-testid="options-list">
          {options.map((option: any) => (
            <div
              key={option.value}
              data-testid={`option-${option.value}`}
              onClick={() => onChange(option)}
            >
              {option.label}
            </div>
          ))}
        </div>
        <div data-testid="combobox-props">
          <span data-testid="item-size-prop">{itemSize}</span>
          <span data-testid="max-height-prop">{maxHeightPx}</span>
        </div>
      </div>
    );
  },
}));

vi.mock("~/api/openapi/generated");
const mockGetClients = vi.mocked(CrmApi.prototype.crmGetClientList);

describe("CrmClientsDropdown", () => {
  const mockOnChange = vi.fn();

  const mockClientsResponse: ClientListResponse = {
    clients: [
      { uuid: "client-1", name: "John Doe", type: "individual", crmId: "1" },
      { uuid: "client-2", name: "Jane Smith", type: "household", crmId: "2" },
      { uuid: "client-3", name: "Acme Corp", type: "household", crmId: "3" },
    ],
    clientSelectionEnabled: true,
    crmSystem: "salesforce",
    nextPageToken: "next-page-token",
  };

  const defaultProps = {
    onChange: mockOnChange,
  };

  beforeEach(() => {
    mockGetClients.mockResolvedValue(mockClientsResponse);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it("renders with default props", () => {
    render(<CrmClientsDropdown {...defaultProps} />);

    expect(screen.getByTestId("virtualized-combobox")).toBeInTheDocument();
    expect(screen.getByTestId("combobox-trigger")).toBeInTheDocument();
    expect(screen.getByText("Select client")).toBeInTheDocument();
  });

  it("renders with custom placeholder", () => {
    render(
      <CrmClientsDropdown {...defaultProps} placeholder="Choose a client" />
    );

    expect(screen.getByText("Choose a client")).toBeInTheDocument();
  });

  it("renders with left icon", () => {
    const leftIcon = <span data-testid="custom-icon">🔍</span>;

    render(<CrmClientsDropdown {...defaultProps} leftIcon={leftIcon} />);

    expect(screen.getByTestId("left-icon")).toBeInTheDocument();
    expect(screen.getByTestId("custom-icon")).toBeInTheDocument();
  });

  it("renders with selected object", () => {
    const selectedClient = {
      uuid: "client-1",
      name: "John Doe",
      type: ClientType.Individual,
    };

    render(
      <CrmClientsDropdown {...defaultProps} selectedClient={selectedClient} />
    );

    expect(screen.getByText("John Doe (individual)")).toBeInTheDocument();
  });

  it("renders in disabled state", () => {
    render(<CrmClientsDropdown {...defaultProps} disabled />);

    const trigger = screen.getByTestId("combobox-trigger");
    expect(trigger).toBeDisabled();
  });

  it("loads clients on first API call", async () => {
    const user = userEvent.setup();
    render(
      <CrmClientsDropdown
        {...defaultProps}
        selectedClient={{
          uuid: "other-client",
          name: "Other Client",
          type: ClientType.Individual,
        }}
      />
    );

    const trigger = screen.getByTestId("combobox-trigger");
    await user.click(trigger);

    await waitFor(() => {
      expect(mockGetClients).toHaveBeenCalledWith(
        expect.objectContaining({ q: "", pageSize: 20, cursor: "" })
      );
    });

    // Make sure the selected option is still rendered and available in the list
    await waitFor(() => {
      expect(screen.getByTestId("option-other-client")).toBeInTheDocument();
    });

    expect(screen.getByTestId("option-client-1")).toBeInTheDocument();
    expect(screen.getByTestId("option-client-2")).toBeInTheDocument();
    expect(screen.getByTestId("option-client-3")).toBeInTheDocument();
    expect(
      screen.getByRole("button", { name: "Other Client (individual)" })
    ).toBeInTheDocument();
    expect(screen.getByTestId("option-other-client")).toHaveTextContent(
      "Other Client"
    );
    expect(screen.getByText("John Doe (individual)")).toBeInTheDocument();
    expect(screen.getByText("Jane Smith (household)")).toBeInTheDocument();
    expect(screen.getByText("Acme Corp (household)")).toBeInTheDocument();
  });

  it("handles client selection", async () => {
    const user = userEvent.setup();
    render(<CrmClientsDropdown {...defaultProps} />);

    // Load the clients
    const trigger = screen.getByTestId("combobox-trigger");
    await user.click(trigger);

    await waitFor(() => {
      expect(screen.getByTestId("option-client-1")).toBeInTheDocument();
    });

    // Select a client
    const option = screen.getByTestId("option-client-1");
    await user.click(option);

    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith({
        uuid: "client-1",
        name: "John Doe",
        type: "individual",
        crmId: "1",
      });
    });

    expect(mockGetClients).toHaveBeenCalledWith(
      expect.objectContaining({ q: "", pageSize: 20, cursor: "" })
    );
  });

  it("handles API error gracefully", async () => {
    const user = userEvent.setup();

    // Mock fetch to reject but catch the error to prevent unhandled rejection
    vi.mocked(CrmApi.prototype.crmGetClientList).mockImplementation(() =>
      Promise.reject(new Error("API Error")).catch(() => {
        // Silently catch the error to prevent unhandled rejection
        return {
          clients: [],
          clientSelectionEnabled: true,
          crmSystem: "salesforce",
        };
      })
    );

    render(<CrmClientsDropdown {...defaultProps} />);

    const trigger = screen.getByTestId("combobox-trigger");

    await user.click(trigger);

    await waitFor(() => {
      expect(screen.getByTestId("virtualized-combobox")).toBeInTheDocument();
    });
  });

  it("handles empty API response", async () => {
    const user = userEvent.setup();

    vi.mocked(CrmApi.prototype.crmGetClientList).mockResolvedValue({
      clients: [],
      clientSelectionEnabled: true,
      crmSystem: "salesforce",
    });

    render(<CrmClientsDropdown {...defaultProps} />);

    const trigger = screen.getByTestId("combobox-trigger");
    await user.click(trigger);

    await waitFor(() => {
      expect(CrmApi.prototype.crmGetClientList).toHaveBeenCalled();
    });

    const optionsList = screen.getByTestId("options-list");
    expect(optionsList).toBeEmptyDOMElement();
  });
});
