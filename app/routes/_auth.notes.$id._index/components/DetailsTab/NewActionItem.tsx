import { Check, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, X } from "lucide-react";
import { useMemo, useState } from "react";
import { useRevalidator } from "react-router";
import { toast } from "react-toastify";
import { But<PERSON> } from "~/@shadcn/ui/button";

import { TextareaGrowable } from "~/@shadcn/ui/textarea";
import { TaskApi, TaskResponse } from "~/api/openapi/generated";
import { useAPIConfiguration } from "~/context/apiAuth";
import { useUserAgent } from "~/context/userAgent";
import { fetchPost } from "~/utils/fetch";

type Props = {
  parentNoteUuid: string;
  userUuid: string;
  onSave: (task: TaskResponse, willAddAnother: boolean) => void;
  onCancel: () => void;
};

const NewActionItem = (props: Props) => {
  const { parentNoteUuid, userUuid, onSave, onCancel } = props;

  const [title, setTitle] = useState("");
  const [isSaving, setIsSaving] = useState(false);

  const revalidator = useRevalidator();
  const { isMobile } = useUserAgent();

  const apiConfig = useAPIConfiguration();
  const taskAPIClient = useMemo(() => new TaskApi(apiConfig), [apiConfig]);

  // save on "ENTER" press
  const onKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.key === "Enter") {
      event.preventDefault();

      // check if Cmd / Ctrl is pressed
      onSaveInternal(event.metaKey || event.ctrlKey);
    }
  };

  const onSaveInternal = async (shouldAddAnother: boolean) => {
    // check for empty title
    if (!title.trim()) {
      return;
    }

    setIsSaving(true);

    try {
      const { taskUuid } = await taskAPIClient.taskCreateTask({
        createTaskRequest: {
          parentNoteUuid,
          title,
          ownerUuid: userUuid,
        },
      });

      toast.success("New action item added");
      revalidator.revalidate();

      if (shouldAddAnother) {
        // reset all input fields
        setTitle("");
      }

      onSave(
        {
          uuid: taskUuid,
          title,
          created: new Date(),
          modified: new Date(),
          completed: false,
          parentNoteUuid,
          owner: null,
          assignee: null,
        },
        shouldAddAnother
      );
    } catch (e) {
      console.log(e);
      toast.error("Failed to create task. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="flex items-start gap-2 rounded-md border border-slate-200 bg-muted p-2">
      <Circle size={20} className="m-0.5 shrink-0 opacity-25" />

      <div className="grow">
        <TextareaGrowable
          className="flex-grow border-none p-2 text-sm !opacity-100 focus:outline-none"
          placeholder={
            isMobile
              ? "Type a new action item"
              : "Type a new action item… Press ENTER (⏎) to save"
          }
          autoFocus
          value={title}
          onChange={(event) => {
            setTitle(event.currentTarget.value);
          }}
          onKeyDown={onKeyDown}
        />

        <div className="mt-2 flex flex-wrap gap-y-1">
          {/* save button */}
          <Button
            variant="outline"
            onClick={() => onSaveInternal(false)}
            disabled={isSaving || !title.trim()}
            size="sm"
            className="mr-1"
          >
            <Check size={14} className="mr-1" />
            {isSaving ? "Saving…" : "Save"}
          </Button>

          {/* save & add button */}
          {!isSaving && (
            <Button
              variant="ghost"
              onClick={() => onSaveInternal(true)}
              disabled={!title.trim()}
              size="sm"
            >
              <CheckCheck size={14} className="mr-1" />
              {isMobile ? "Save & Add" : "Save & Add another"}
            </Button>
          )}

          {/* cancel button */}
          {!isSaving && (
            <Button variant="ghost" onClick={onCancel} size="sm">
              <X size={14} className="mr-1" />
              Cancel
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default NewActionItem;
