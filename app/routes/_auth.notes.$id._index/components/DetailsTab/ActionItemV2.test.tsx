import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { toast } from "react-toastify";

import ActionItemV2 from "./ActionItemV2";
import { UserAgentProvider } from "~/context/userAgent";
import { TaskApi, TaskResponse } from "~/api/openapi/generated";

// Mock external dependencies
vi.mock("react-toastify", () => ({
  toast: {
    error: vi.fn(),
  },
}));

const mockRevalidate = vi.fn();

vi.mock("react-router", async (importOriginal) => {
  const actual = await importOriginal<typeof import("react-router")>();
  return {
    ...actual,
    useRevalidator: () => ({ revalidate: mockRevalidate }),
  };
});

vi.mock("~/api/openapi/generated");
const mockGetTask = vi.mocked(TaskApi.prototype.taskViewTask);
const mockEditTask = vi.mocked(TaskApi.prototype.taskEditTask);

// Mock Combobox component
vi.mock("~/@ui/Combobox", () => ({
  Combobox: ({
    testId,
    options,
    onChange,
    selected,
    placeholder,
    triggerClassName,
  }: any) => (
    <div data-testid="combobox" className={triggerClassName}>
      <button
        onClick={() => {
          // For testing, we'll simulate selecting the second option when clicked
          if (options.length > 1) {
            onChange(options[1]?.value);
          }
        }}
        data-testid={testId}
      >
        {selected
          ? options.find((opt: any) => opt.value === selected)?.label
          : placeholder}
      </button>
      {options.map((option: any) => (
        <div
          key={option.value}
          data-testid={`option-${option.value}`}
          onClick={() => onChange(option.value)}
        >
          {option.label}
        </div>
      ))}
    </div>
  ),
}));

// Mock DatePicker component
vi.mock("~/@ui/DatePicker", () => ({
  DatePicker: ({ date, onSelect, triggerClassName }: any) => (
    <div data-testid="date-picker" className={triggerClassName}>
      <button
        onClick={() => onSelect(new Date("2024-12-31"))}
        data-testid="date-picker-trigger"
      >
        {date ? date.toLocaleDateString() : "Select date"}
      </button>
    </div>
  ),
}));

describe("ActionItemV2", () => {
  const mockTaskData = {
    uuid: "task-123",
    title: "Test Action Item",
    dueDate: new Date("2024-12-25T00:00:00.000Z"),
    completed: false,
    assignee: {
      uuid: "user-1",
      name: "John Doe",
    },
    created: new Date(),
    modified: new Date(),
    parentNoteUuid: "note-123",
    owner: { uuid: "owner-1", name: "Owner" },
    assignees: [
      { uuid: "user-1", name: "John Doe" },
      { uuid: "user-2", name: "Jane Smith" },
    ],
  } as TaskResponse;

  const defaultProps = {
    parentNoteUuid: "note-123",
    data: mockTaskData,
    disabled: false,
    onDelete: vi.fn(),
  };

  const renderWithUserAgent = (
    component: React.ReactElement,
    isMobile = false
  ) => {
    const userAgentValue = {
      isMobile,
      isAndroid: false,
      isIos: false,
      isFirefox: false,
      isChrome: true,
      isSafari: false,
    };

    return render(
      <UserAgentProvider value={userAgentValue}>{component}</UserAgentProvider>
    );
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Set timezone to UTC for consistent date tests
    vi.stubEnv("TZ", "UTC");

    // Mock the fetch call for getting the task details to get the assignees list.
    //
    // Only the assignees are used, but we need to return a full valid object.
    mockGetTask.mockResolvedValue({
      assignee: { uuid: "user-1", name: "John Doe" },
      completed: false,
      created: new Date(),
      modified: new Date(),
      title: "Test Action Item",
      uuid: "task-123",
      dueDate: new Date("2024-12-25T00:00:00.000Z"),
      parentNoteUuid: "note-123",
      owner: { uuid: "owner-1", name: "Owner" },
      assignees: [
        { uuid: "user-1", name: "John Doe" },
        { uuid: "user-2", name: "Jane Smith" },
      ],
    });

    // Mock fetchPost to return an empty successful response.
    vi.mocked(mockEditTask).mockResolvedValue(undefined);
  });

  afterEach(() => {
    vi.restoreAllMocks();
    vi.unstubAllEnvs();
  });

  it("renders the action item with correct initial state", async () => {
    renderWithUserAgent(<ActionItemV2 {...defaultProps} />);

    expect(screen.getByText("Test Action Item")).toBeInTheDocument();
    expect(screen.getByText("12/25/2024")).toBeInTheDocument();

    // Wait for assignees to load and be displayed in the combobox trigger
    await waitFor(() => {
      expect(screen.getByTestId("assignee-selection")).toHaveTextContent(
        "John Doe"
      );
    });

    // Check that the task is not completed (Circle icon should be present)
    // The toggle is a div with cursor-pointer class
    const container = screen
      .getByText("Test Action Item")
      .closest('div[class*="group"]');
    const toggleDiv = container?.querySelector('div[class*="cursor-pointer"]');
    expect(toggleDiv).toBeInTheDocument();
  });

  it("renders completed task with correct styling", () => {
    const completedTask = { ...mockTaskData, completed: true };
    renderWithUserAgent(
      <ActionItemV2 {...defaultProps} data={completedTask} />
    );

    // The line-through class is applied to the parent div, not the span
    const titleContainer = screen
      .getByText("Test Action Item")
      .closest('div[class*="line-through"]');
    expect(titleContainer).toHaveClass("line-through");
  });

  it("toggles task completion when checkbox is clicked", async () => {
    const user = userEvent.setup();

    renderWithUserAgent(<ActionItemV2 {...defaultProps} />);

    // Find the circle icon (toggle button) specifically
    const toggleButton = screen.getByTestId("action-item-v2-toggle");
    await user.click(toggleButton);

    await waitFor(() => {
      expect(mockEditTask).toHaveBeenCalledWith({
        taskUuid: "task-123",
        taskUpdate: { completed: true, parentNoteUuid: "note-123" },
      });
    });

    expect(mockRevalidate).toHaveBeenCalled();
  });

  it("shows error toast when task completion toggle fails", async () => {
    const user = userEvent.setup();
    vi.mocked(mockEditTask).mockRejectedValue(new Error("Network error"));

    renderWithUserAgent(<ActionItemV2 {...defaultProps} />);

    // Find the circle icon (toggle button) specifically
    const toggleButton = screen.getByTestId("action-item-v2-toggle");
    await user.click(toggleButton);

    await waitFor(() => {
      expect(vi.mocked(toast.error)).toHaveBeenCalledWith(
        "Could not update task"
      );
    });
  });

  it("enters edit mode when edit icon is clicked", async () => {
    const user = userEvent.setup();
    renderWithUserAgent(<ActionItemV2 {...defaultProps} />);

    // Find the edit icon specifically (it's an svg element)
    const editButton = screen
      .getByText("Test Action Item")
      .parentElement?.querySelector("svg");

    expect(editButton).not.toBeFalsy();
    await user.click(editButton as Element);

    // Should show textarea in edit mode
    expect(screen.getByDisplayValue("Test Action Item")).toBeInTheDocument();
    expect(
      screen.getByPlaceholderText("Add an action item")
    ).toBeInTheDocument();
  });

  it("saves title changes when Enter is pressed", async () => {
    const user = userEvent.setup();

    renderWithUserAgent(<ActionItemV2 {...defaultProps} />);

    // Enter edit mode
    const editButton = screen
      .getByText("Test Action Item")
      .parentElement?.querySelector("svg");
    if (editButton) {
      await user.click(editButton);

      const textarea = screen.getByDisplayValue("Test Action Item");
      await user.clear(textarea);
      await user.type(textarea, "Updated Action Item");
      await user.keyboard("{Enter}");

      await waitFor(() => {
        expect(mockEditTask).toHaveBeenCalledWith({
          taskUuid: "task-123",
          taskUpdate: {
            title: "Updated Action Item",
            parentNoteUuid: "note-123",
          },
        });
      });
    }
  });

  it("saves title changes when textarea loses focus", async () => {
    const user = userEvent.setup();

    renderWithUserAgent(<ActionItemV2 {...defaultProps} />);

    // Enter edit mode
    const editButton = screen
      .getByText("Test Action Item")
      .parentElement?.querySelector("svg");
    if (editButton) {
      await user.click(editButton);

      const textarea = screen.getByDisplayValue("Test Action Item");
      await user.clear(textarea);
      await user.type(textarea, "Updated Action Item");

      // Click outside to blur
      await user.click(document.body);

      await waitFor(() => {
        expect(mockEditTask).toHaveBeenCalledWith({
          taskUuid: "task-123",
          taskUpdate: {
            title: "Updated Action Item",
            parentNoteUuid: "note-123",
          },
        });
      });
    }
  });

  it("shows error toast when title update fails", async () => {
    const user = userEvent.setup();
    vi.mocked(mockEditTask).mockRejectedValue(new Error("Network error"));

    renderWithUserAgent(<ActionItemV2 {...defaultProps} />);

    // Enter edit mode
    const editButton = screen
      .getByText("Test Action Item")
      .parentElement?.querySelector("svg");
    if (editButton) {
      await user.click(editButton);

      const textarea = screen.getByDisplayValue("Test Action Item");
      await user.clear(textarea);
      await user.type(textarea, "Updated Action Item");
      await user.keyboard("{Enter}");

      await waitFor(() => {
        expect(vi.mocked(toast.error)).toHaveBeenCalledWith(
          "Could not update title"
        );
      });
    }
  });

  it("updates assignee when selected from combobox", async () => {
    const user = userEvent.setup();

    renderWithUserAgent(<ActionItemV2 {...defaultProps} />);

    // Wait for assignees to load
    await waitFor(() => {
      expect(screen.getByTestId("assignee-selection")).toBeInTheDocument();
    });

    // Click the combobox trigger which will select the second option (user-2)
    const comboboxTrigger = screen.getByTestId("assignee-selection");
    await user.click(comboboxTrigger);

    await waitFor(() => {
      expect(mockEditTask).toHaveBeenCalledWith({
        taskUuid: "task-123",
        taskUpdate: {
          parentNoteUuid: "note-123",
          assignee: "user-2",
        },
      });
    });
  });

  it("shows error toast when assignee update fails", async () => {
    const user = userEvent.setup();
    vi.mocked(mockEditTask).mockRejectedValue(new Error("Network error"));

    renderWithUserAgent(<ActionItemV2 {...defaultProps} />);

    // Wait for assignees to load
    await waitFor(() => {
      expect(screen.getByTestId("assignee-selection")).toBeInTheDocument();
    });

    // Click the combobox trigger which will try to select the second option (user-2)
    const comboboxTrigger = screen.getByTestId("assignee-selection");
    await user.click(comboboxTrigger);

    await waitFor(() => {
      expect(vi.mocked(toast.error)).toHaveBeenCalledWith(
        "Could not update assignee"
      );
    });
  });

  it("updates due date when selected from date picker", async () => {
    const user = userEvent.setup();

    renderWithUserAgent(<ActionItemV2 {...defaultProps} />);

    const datePickerTrigger = screen.getByTestId("date-picker-trigger");
    await user.click(datePickerTrigger);

    await waitFor(() => {
      expect(mockEditTask).toHaveBeenCalledWith({
        taskUuid: "task-123",
        taskUpdate: {
          parentNoteUuid: "note-123",
          dueDate: new Date("2024-12-31T00:00:00.000Z"),
        },
      });
    });
  });

  it("shows error toast when due date update fails", async () => {
    const user = userEvent.setup();
    vi.mocked(mockEditTask).mockRejectedValue(new Error("Network error"));

    renderWithUserAgent(<ActionItemV2 {...defaultProps} />);

    const datePickerTrigger = screen.getByTestId("date-picker-trigger");
    await user.click(datePickerTrigger);

    await waitFor(() => {
      expect(vi.mocked(toast.error)).toHaveBeenCalledWith(
        "Could not update due date"
      );
    });
  });

  it("renders mobile view correctly", () => {
    renderWithUserAgent(<ActionItemV2 {...defaultProps} />, true);

    // Mobile view should not show "Assignee" and "Due Date" labels
    expect(screen.queryByText("Assignee")).not.toBeInTheDocument();
    expect(screen.queryByText("Due Date")).not.toBeInTheDocument();

    // Should show mobile view button
    const mobileViewButton = screen.getByRole("button", { name: "" });
    expect(mobileViewButton).toBeInTheDocument();
  });

  it("navigates to task detail when view button is clicked", async () => {
    const user = userEvent.setup();

    // Mock window.location.href
    delete (window as any).location;
    window.location = { href: "" } as any;

    renderWithUserAgent(<ActionItemV2 {...defaultProps} />);

    // Find the ArrowUpRight icon button
    const viewButtons = screen.getAllByRole("button", { name: "" });
    const viewButton = viewButtons.find((button) =>
      button.querySelector("svg")?.classList.contains("lucide-arrow-up-right")
    );

    if (viewButton) {
      await user.click(viewButton);
      expect(window.location.href).toBe("/tasks/task-123");
    }
  });

  it("shows processing state correctly", () => {
    renderWithUserAgent(<ActionItemV2 {...defaultProps} />);

    const container = screen.getByText("Test Action Item").closest("div");
    expect(container).not.toHaveClass("pointer-events-none opacity-35");
  });

  it("handles disabled state correctly", () => {
    renderWithUserAgent(<ActionItemV2 {...defaultProps} disabled={true} />);

    // The disabled state applies opacity-25 to the toggle button div
    const container = screen
      .getByText("Test Action Item")
      .closest('div[class*="group"]');
    const toggleDiv = container?.querySelector('div[class*="cursor-pointer"]');
    expect(toggleDiv).toHaveClass("opacity-25");
  });

  it("does not update title when empty", async () => {
    const user = userEvent.setup();

    renderWithUserAgent(<ActionItemV2 {...defaultProps} />);

    // Enter edit mode
    const editButton = screen
      .getByText("Test Action Item")
      .parentElement?.querySelector("svg");
    if (editButton) {
      await user.click(editButton);

      const textarea = screen.getByDisplayValue("Test Action Item");
      await user.clear(textarea);
      await user.keyboard("{Enter}");

      // Should not call fetchPost when title is empty
      expect(mockEditTask).not.toHaveBeenCalled();
    }
  });

  it("renders without assignee when none provided", () => {
    const taskWithoutAssignee = { ...mockTaskData, assignee: null };
    renderWithUserAgent(
      <ActionItemV2 {...defaultProps} data={taskWithoutAssignee} />
    );

    expect(screen.getByText("Test Action Item")).toBeInTheDocument();
  });

  it("renders without due date when none provided", () => {
    const taskWithoutDueDate = { ...mockTaskData, dueDate: null };
    renderWithUserAgent(
      <ActionItemV2 {...defaultProps} data={taskWithoutDueDate} />
    );

    expect(screen.getByText("Test Action Item")).toBeInTheDocument();
    expect(screen.getByText("Select date")).toBeInTheDocument();
  });

  it("shows spinner when processing", async () => {
    const user = userEvent.setup();

    // Mock a delayed response to show processing state
    vi.mocked(mockEditTask).mockImplementation(
      () => new Promise((resolve) => setTimeout(() => resolve(undefined), 100))
    );

    renderWithUserAgent(<ActionItemV2 {...defaultProps} />);

    const circleIcon = screen.getByRole("button", { name: "" });
    const toggleButton = circleIcon.closest('div[class*="cursor-pointer"]');

    if (toggleButton) {
      await user.click(toggleButton);

      // Should show processing state on the main container
      const container = screen
        .getByText("Test Action Item")
        .closest('div[class*="group"]');
      expect(container).toHaveClass("pointer-events-none opacity-35");
    }
  });
});
