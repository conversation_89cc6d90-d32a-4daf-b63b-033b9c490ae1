// TODO: @<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Remove the "V2" aspect once we are happy with the new implementation
import { useEffect, useMemo, useState } from "react";
import {
  ArrowUpRight,
  Calendar,
  Circle,
  CircleCheckBig,
  Edit,
  Trash2,
  User,
  UserCog,
} from "lucide-react";

import { TextareaGrowable } from "~/@shadcn/ui/textarea";
import { cn } from "~/@shadcn/utils";
import { Spinner } from "~/@ui/assets/Spinner";
import { useUserAgent } from "~/context/userAgent";
import { useRevalidator } from "react-router";
import { Combobox } from "~/@ui/Combobox";
import { DatePicker } from "~/@ui/DatePicker";
import { toast } from "react-toastify";
import {
  TaskApi,
  TaskResponse,
  AssigneeType,
  TaskUpdate,
} from "~/api/openapi/generated";
import { useAPIConfiguration } from "~/context/apiAuth";

type Props = {
  parentNoteUuid: string;
  data: TaskResponse;
  disabled: boolean;
  onDelete: (uuid: string) => void;
};

const ActionItemV2 = ({ parentNoteUuid, data, disabled, onDelete }: Props) => {
  const {
    uuid,
    title: titleProp,
    dueDate: dueDateProp,
    completed,
    assignee: assigneeProp,
    assigneeType: assigneeTypeProp,
    assignees,
  } = data;
  const [isProcessing, setIsProcessing] = useState(false);

  const [title, setTitle] = useState(titleProp);
  const [checked, setChecked] = useState(completed);
  const [assignee, setAssignee] = useState<Assignee | null>(assigneeProp);
  const [dueDate, setDueDate] = useState<Date | undefined>(
    dueDateProp ? new Date(dueDateProp) : undefined
  );
  const [assigneeType, setAssigneeType] = useState<
    AssigneeType | null | undefined
  >(assigneeTypeProp);

  const [isEditingTitle, setIsEditingTitle] = useState(false);

  const { isMobile } = useUserAgent();
  const revalidator = useRevalidator();

  const apiConfig = useAPIConfiguration();
  const taskAPIClient = useMemo(() => new TaskApi(apiConfig), [apiConfig]);

  // Sync local state with prop changes (e.g., after revalidation)
  useEffect(() => {
    setTitle(titleProp);
    setAssignee(assigneeProp);
  }, [titleProp, assigneeProp]);

  const toggleChecked = async () => {
    const value = !checked;
    setChecked(value);

    const isSaved = await saveChanges({ completed: value });

    if (!isSaved) {
      setChecked(completed); // reset
      toast.error("Could not update task");
    }
  };

  const updateAssignee = async (assignee: Assignee) => {
    const isSaved = await saveChanges({ assignee: assignee.uuid });

    if (isSaved) {
      setAssignee(assignee);
    } else {
      toast.error("Could not update assignee");
    }
  };

  const updateDate = async (nextDate?: Date) => {
    if (!nextDate) {
      return;
    }

    const isSaved = await saveChanges({ dueDate: nextDate });

    if (isSaved) {
      setDueDate(nextDate);
    } else {
      toast.error("Could not update due date");
    }
  };

  const updateAssigneeType = async (nextType?: AssigneeType) => {
    const isSaved = await saveChanges({
      assigneeType: nextType,
    });

    if (isSaved) {
      setAssigneeType(nextType);
    } else {
      toast.error("Could not update assignee type");
    }
  };

  const updateTitle = async () => {
    if (!title) {
      // TODO: @debojyotighosh ENG-1895 Offer to delete
      return;
    }

    const isSaved = await saveChanges({ title });

    if (!isSaved) {
      setTitle(titleProp);
      toast.error("Could not update title");
    }
  };

  const saveChanges = async (taskUpdate: TaskUpdate) => {
    return new Promise((resolve, _) => {
      let couldSave = false;
      setIsProcessing(true);

      taskAPIClient
        .taskEditTask({
          taskUuid: uuid,
          taskUpdate: { ...taskUpdate, parentNoteUuid },
        })
        .then(() => {
          couldSave = true;
          revalidator.revalidate();
        })
        .catch(() => {
          // do nothing; couldSave is false
        })
        .finally(() => {
          setIsProcessing(false);
          resolve(couldSave);
        });
    });
  };

  const deleteItem = () => {
    // ignore if editing title
    if (isEditingTitle) {
      return;
    }

    setIsProcessing(true);

    taskAPIClient
      .taskDeleteTask({ taskUuid: uuid })
      .then(() => {
        toast.success("Task deleted");

        // remove from local state of parent
        onDelete(uuid);

        // update from server
        revalidator.revalidate();
      })
      .catch(() => {
        toast.error("Could not delete task");
      })
      .finally(() => {
        setIsProcessing(false);
      });
  };

  return (
    <div
      className={cn(
        "group flex items-start gap-2 rounded-md border border-slate-200 p-2 hover:bg-muted",
        checked && "opacity-80",
        isProcessing && "pointer-events-none opacity-35"
      )}
    >
      {/* toggle */}
      <div
        className={cn(
          "flex size-6 shrink-0 cursor-pointer items-center justify-center",
          disabled && "opacity-25"
        )}
        onClick={toggleChecked}
        data-testid="action-item-v2-toggle"
      >
        {isProcessing && <Spinner />}
        {!isProcessing &&
          (checked ? (
            <CircleCheckBig size={20} className="text-success" />
          ) : (
            <Circle size={20} />
          ))}
      </div>

      <div className="grow">
        {/* title */}
        {isEditingTitle ? (
          <TextareaGrowable
            value={title}
            placeholder="Add an action item"
            className="flex-grow border-none text-sm !opacity-100 focus:outline-none"
            onChange={(event) => {
              setTitle(event.currentTarget.value);
            }}
            onBlur={() => {
              setIsEditingTitle(false);

              // check for empty title
              if (!title.trim()) {
                setTitle(titleProp);
                return;
              }

              updateTitle();
            }}
            onKeyDown={(event) => {
              if (event.key === "Enter") {
                event.preventDefault();

                // check for empty title
                if (!title.trim()) {
                  return;
                }

                setIsEditingTitle(false);
                updateTitle();
              }
            }}
            autoFocus
          />
        ) : (
          <div className={cn("flex w-full text-sm", checked && "line-through")}>
            <span>
              <span className="mr-2">{title}</span>

              {/* edit icon */}
              <Edit
                size={20}
                className={cn(
                  "-mt-0.5 inline shrink-0 cursor-pointer align-middle text-muted-foreground",
                  !isMobile &&
                    "opacity-25 transition-opacity duration-200 group-hover:opacity-100"
                )}
                onClick={() => setIsEditingTitle(true)}
              />
            </span>
          </div>
        )}

        <div className="mt-2 flex shrink-0 flex-col gap-1 sm:flex-row sm:gap-4 md:mt-1 md:items-center">
          {/* assignee */}
          {assignees ? (
            <div className="flex items-center gap-1">
              <User size={16} className="shrink-0 text-muted-foreground" />
              {!isMobile && (
                <span className="text-sm text-muted-foreground">Assignee</span>
              )}

              <Combobox
                testId="assignee-selection"
                placeholder=""
                options={assignees.map(({ name, uuid }) => ({
                  label: name,
                  value: uuid,
                }))}
                selected={assignee?.uuid}
                onChange={(nextValue) => {
                  const selectedAssignee = (assignees || []).find(
                    (ele) => ele.uuid === nextValue
                  );
                  if (selectedAssignee) {
                    updateAssignee(selectedAssignee);
                  }
                }}
                triggerClassName="py-1 px-2 text-xs min-w-32 w-fit ml-1"
              />
            </div>
          ) : (
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              Loading assignees…
            </div>
          )}

          {!isMobile && (
            <span className="h-4 w-[1px] bg-muted-foreground opacity-50" />
          )}

          <div className="flex items-center gap-1">
            {/* due date */}
            <Calendar size={16} className="shrink-0 text-muted-foreground" />
            {!isMobile && (
              <span className="text-sm text-muted-foreground">Due Date</span>
            )}
            <DatePicker
              id={`task-due-date-${uuid}`}
              name="date"
              date={dueDate}
              onSelect={(nextDate) => updateDate(nextDate)}
              triggerClassName="py-1 px-2 text-xs min-h-fit ml-1"
              showIcon={false}
            />
          </div>

          {!isMobile && (
            <span className="h-4 w-[1px] bg-muted-foreground opacity-50" />
          )}

          <div className="flex grow items-center gap-1">
            {/* assignee type */}
            <UserCog size={16} className="shrink-0 text-muted-foreground" />
            {!isMobile && (
              <span className="text-sm text-muted-foreground">Type</span>
            )}

            <Combobox
              testId="assignee-type-selection"
              placeholder="Pick a type"
              options={actionItemAssigneeTypeOptions}
              selected={assigneeType as string}
              onChange={(nextValue) => {
                updateAssigneeType(nextValue as AssigneeType);
              }}
              triggerClassName="py-1 px-2 text-xs min-w-32 w-fit ml-1"
              placeholderClassname="text-xs"
            />

            {/* view button for mobile */}
            {isMobile && (
              <div className="ml-auto flex items-center gap-4 self-end text-muted-foreground sm:self-center">
                <Trash2 size={18} onClick={deleteItem} />
                <ArrowUpRight
                  size={24}
                  onClick={() => (window.location.href = `/tasks/${uuid}`)}
                  role="button"
                />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* view icon for desktop */}
      {!isMobile && (
        <div className="ml-auto flex shrink-0 items-center gap-3 text-muted-foreground">
          <Trash2
            size={18}
            className={cn(
              "cursor-pointer",
              !isMobile &&
                "opacity-25 transition-opacity duration-200 group-hover:text-destructive group-hover:opacity-100"
            )}
            onClick={deleteItem}
          />
          <ArrowUpRight
            size={24}
            className={cn(
              "cursor-pointer",
              !isMobile &&
                "opacity-25 transition-opacity duration-200 group-hover:opacity-100"
            )}
            onClick={() => (window.location.href = `/tasks/${uuid}`)}
            role="button"
          />
        </div>
      )}
    </div>
  );
};

type Assignee = {
  uuid: string;
  name: string;
};

const actionItemAssigneeTypeOptions = [
  {
    label: "Advisor Task",
    value: AssigneeType.AdvisorTask,
  },
  {
    label: "Client Follow-up",
    value: AssigneeType.ClientFollowup,
  },
];

export default ActionItemV2;
