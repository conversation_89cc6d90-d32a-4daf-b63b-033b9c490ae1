import { useCallback, useMemo, useState } from "react";
import EntityTag from "~/@ui/EntityTag";

import { VirtualizedCombobox } from "~/@ui/VirtualizedCombobox";
import { ApiRoutersCrmClientResponse, CrmApi } from "~/api/openapi/generated";
import { useAPIConfiguration } from "~/context/apiAuth";
import LabeledValue from "~/types/LabeledValue";

type CrmClientsDropdownType = {
  placeholder?: string;
  leftIcon?: React.ReactNode;
  onChange: (value: ApiRoutersCrmClientResponse | undefined) => void;
  selectedClient?: ApiRoutersCrmClientResponse;
  modal?: boolean;
  disabled?: boolean;
  itemSize?: number;
  maxHeightPx?: number;
  triggerClassName?: string;
};

const CrmClientsDropdown = (props: CrmClientsDropdownType) => {
  const {
    placeholder,
    leftIcon,
    onChange,
    selectedClient,
    modal,
    disabled,
    itemSize = 40,
    maxHeightPx = 200,
    triggerClassName,
  } = props;

  const [clients, setClients] = useState<ApiRoutersCrmClientResponse[]>([]);
  const [lastSearchTerm, setLastSearchTerm] = useState("");
  const [cursor, setCursor] = useState<string | null>("");
  const [isFirstApiCall, setIsFirstApiCall] = useState(true); // true if this is the first time the API is called

  const apiConfig = useAPIConfiguration();
  const apiClient = useMemo(() => {
    return new CrmApi(apiConfig);
  }, [apiConfig]);

  const loadClientsForCombobox = useCallback(
    async (searchTerm: string) => {
      let updatedSearchTerm = lastSearchTerm;
      let updatedCursor = cursor;
      let hasSearchTermChanged = false;

      if (lastSearchTerm !== searchTerm) {
        hasSearchTermChanged = true;
        updatedSearchTerm = searchTerm;
        updatedCursor = null;
      }

      setLastSearchTerm(updatedSearchTerm);
      setCursor(updatedCursor);

      if (!hasSearchTermChanged && !updatedCursor && !isFirstApiCall) {
        return;
      }

      setIsFirstApiCall(false);

      const { clients: options, nextPageToken } =
        await apiClient.crmGetClientList({
          q: updatedSearchTerm,
          pageSize: 20,
          cursor: updatedCursor,
        });

      const optionsWithCrmId = options.filter(
        ({ crmId }: ApiRoutersCrmClientResponse) => !!crmId
      );

      setCursor(nextPageToken ?? null);
      let newClientList = hasSearchTermChanged
        ? optionsWithCrmId
        : [...clients, ...optionsWithCrmId];
      if (
        selectedClient &&
        !newClientList.some(
          (c: ApiRoutersCrmClientResponse) => c.uuid === selectedClient.uuid
        )
      ) {
        newClientList = [selectedClient, ...newClientList];
      }
      setClients(newClientList);
    },
    [lastSearchTerm, cursor, isFirstApiCall, selectedClient, clients, apiClient]
  );

  const labelForClient = (
    client: ApiRoutersCrmClientResponse,
    isSelected?: boolean
  ): {
    label: string;
    value: string;
    row: React.ReactNode;
  } => {
    return {
      // Don't show "unknown" type in the label
      label:
        client.type && client.type.toLowerCase() !== "unknown"
          ? `${client.name} (${client.type})`
          : client.name,
      value: client.uuid,
      row: (
        <EntityTag
          title={client.name}
          onClick={() => {}}
          badgeText={client.type ? `client (${client.type})` : "client"}
        >
          {client.name}
        </EntityTag>
      ),
    };
  };

  const options = clients.map((client) => labelForClient(client));

  return (
    <VirtualizedCombobox
      options={options}
      placeholder={placeholder}
      leftIcon={leftIcon}
      onChange={(item) => {
        const selectedClient = clients.find((c) => c.uuid === item?.value);
        onChange(selectedClient);
      }}
      disabled={disabled}
      selectedObject={
        selectedClient ? labelForClient(selectedClient, true) : undefined
      }
      loadOptions={loadClientsForCombobox}
      modal={modal}
      itemSize={itemSize}
      maxHeightPx={maxHeightPx}
      triggerClassName={triggerClassName}
    />
  );
};

export default CrmClientsDropdown;
