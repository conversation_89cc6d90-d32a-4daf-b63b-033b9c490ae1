import { use<PERSON><PERSON>back, useContext, useEffect, useRef, useState } from "react";
import { useEventSource } from "remix-utils/sse/react";
import {
  LoaderFunctionArgs,
  data,
  redirect,
  useRevalidator,
  type ActionFunctionArgs,
  type MetaFunction,
} from "react-router";
import { SerializeFrom } from "~/types/remix";
import {
  unstable_usePrompt,
  useActionData,
  useBeforeUnload,
  useFetcher,
  useLoaderData,
  useLocation,
  useNavigate,
  useNavigation,
  useParams,
  useSearchParams,
  useSubmit,
} from "react-router";
import { Steps } from "intro.js-react";

import { logError } from "~/utils/log.server";
import {
  RecorderCard,
  RecorderContext,
  RecorderProvider,
} from "~/routes/_auth.notes.create.($id)/Recorder";
import { AudioFileUploader } from "~/routes/_auth.notes.create.($id)/AudioFileUploader";
import { BackButton } from "~/@ui/buttons/BackButton";
import { SaveButton } from "~/@ui/buttons/SaveButton";
import { HeaderV2, SidebarV2 } from "~/@ui/layout/LayoutV2";
import {
  FormControl,
  FormDescription,
  FormField,
  FormLabel,
} from "~/@shadcn/ui/form";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "~/@shadcn/ui/tooltip";
import {
  Calendar,
  ChevronDown,
  ChevronUp,
  Lightbulb,
  Sparkles,
  Users,
} from "lucide-react";
import { AfterHydration } from "~/utils/hydration";
import { Typography } from "~/@ui/Typography";
import { addHours, format } from "date-fns";
import { getPayloadFromForm } from "~/routes/_auth.notes.create.($id)/utils";
import getMeetingNameFromAttendees from "./utils/getMeetingNameFromAttendees";
import tutorialSteps from "./utils/tutorialSteps";
import { getNoteById } from "~/api/notes/getNoteById.server";
import { Tabs, TabsList, TabsTrigger } from "~/@shadcn/ui/tabs";
import { NoteTakerController } from "~/routes/_auth.notes.create.($id)/NoteTakerController";
import { ActionTypes } from "~/utils/const";
import { isValidTitle } from "~/utils/validation";
import { getAttendeeOptions } from "~/api/attendees/getAttendeeOptions.server";
import { AttendeeOptions, AttendeeOptionsStruct } from "~/api/attendees/types";
import { AttendeesMultiSelect } from "~/@ui/attendees/AttendeeMultiSelect";
import { Id, toast } from "react-toastify";
import { getUserSessionOrRedirect } from "~/auth/authenticator.server";
import { getEventStreamParams } from "~/api/bots/events.server";
import { cn } from "~/@shadcn/utils";
import { configurationParameters } from "~/api/openapi/configParams";
import {
  Bot,
  BotApi,
  BotMeetingType,
  BotStatus,
  ClientInteraction,
  Configuration,
  FollowUpStatus,
  MeetingArtifactsApi,
  MeetingCategory,
  MeetingType,
  MeetingTypesResponse,
  NoteApi,
  NoteResponse,
  ProcessingStatus,
} from "~/api/openapi/generated";
import { resolvedAttendeeOptions } from "~/utils/attendeeOptions";
import { ConfirmModal } from "~/@ui/ConfirmModal";
import { DeleteButton } from "~/@ui/buttons/DeleteButton";
import isValidPhoneNumber from "libphonenumber-js";
import "react-phone-number-input/style.css";
import { useFlag } from "~/context/flags";
import { MeetingTypeSelector } from "./components/MeetingTypeSelector";
import { ToggleGroup, ToggleGroupItem } from "~/@shadcn/ui/toggle-group";
import MeetingPrepTab from "./components/MeetingPrepTab";
import MeetingPrepModal from "./components/MeetingPrepModal";
import safeAtob from "~/utils/safeAtob";
import useOnboarding from "~/utils/useOnboarding";
import { Separator } from "~/@shadcn/ui/separator";
import { fetchPost } from "~/utils/fetch";
import { datadogLogs } from "@datadog/browser-logs";
import { useUserAgent } from "~/context/userAgent";
import { Button } from "~/@shadcn/ui/button";
import { Spinner } from "~/@ui/assets/Spinner";
import { followUpDataForFollowUp } from "~/routes/_auth.notes.$id._index/followUpDataTypes";
import { isFlagEnabled } from "~/utils/flagsInCookies";
import getResolvedMeetingTypes from "./utils/getResolvedMeetingTypes";
import ActionButtonDrawer from "./components/ActionButtonDrawer";

// Exports
export const meta: MetaFunction = () => [
  { title: "Create note" },
  { name: "description", content: "Create new note" },
];

const fragmentDurationSeconds = 10;

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    const noteID = new URL(request.url).searchParams.get("noteID");
    const createFormData = await getPayloadFromForm(request);

    const configuration = new Configuration(
      await configurationParameters(request)
    );
    const noteAPI = new NoteApi(configuration);

    const action = createFormData.action ?? ActionTypes.SAVE_NOTE;

    const isUnifyAdvisorHubAndNotesEnabled = isFlagEnabled(
      request,
      "EnableUnifyAdvisorHubAndNotes"
    );

    switch (action) {
      case ActionTypes.RESUME_RECORDING:
        if (!createFormData.botId) {
          throw Error("Bot action requested without a bot ID");
        }
        await new BotApi(configuration).botResumeRecording({
          botUuid: createFormData.botId,
        });
        return null;
      case ActionTypes.PAUSE_RECORDING:
        if (!createFormData.botId) {
          throw Error("Bot action requested without a bot ID");
        }
        await new BotApi(configuration).botPauseRecording({
          botUuid: createFormData.botId,
        });
        return null;
      case ActionTypes.SAVE_NOTE:
      case ActionTypes.CREATE_DRAFT_NOTE:
      case ActionTypes.SAVE_NOTE_AND_START_MEETING:
      case ActionTypes.SAVE_NOTE_AND_LEAVE_MEETING:
      case ActionTypes.SAVE_NOTE_AND_REDIRECT_TO_HUB:
        // Create or update the note.
        const res = await noteAPI.noteCreateOrUpdateNote({
          noteId: noteID,
          meetingType: createFormData.meetingType,
          meetingName: createFormData.meetingName,
          attendees: createFormData.attendees,
          meetingLink: createFormData.meetingLink,
          meetingSourceId: createFormData.meetingSourceID,
          scheduledStartTime: createFormData.startTime,
          scheduledEventUuid: createFormData.scheduledEventUUID,
          scheduledEndTime: createFormData.endTime,
          audioData: createFormData.fileContent,
          fileType: createFormData.fileType,
          clientId: createFormData.linkedCRMEntityID,
          clientName: createFormData.linkedCRMEntityName,
        });

        // NOTE:
        // While "Meeting Prep" does seem to get generated when a note is created,
        // this separate API call is required to update the "CRM entities" (`clientIds`)
        if (createFormData.clientIds) {
          await noteAPI.noteGenerateMeetingPrep({
            noteId: res.noteId,
            generateMeetingPrepRequest: {
              clientIds: createFormData.clientIds.split(","),
            },
          });
        }

        // If the note is completed, redirect to the notes list.
        if (res.completed) {
          return redirect(
            isUnifyAdvisorHubAndNotesEnabled ? "/dashboard" : "/notes"
          );
        }

        // If we are creating a draft note, do not redirect, but return the ID of the draft note in
        // the action response.
        if (action === ActionTypes.CREATE_DRAFT_NOTE) {
          return data({ draftNoteID: res.noteId });
        }

        // Trigger the requested bot action (if there is one).
        if (action === ActionTypes.SAVE_NOTE_AND_START_MEETING) {
          if (!res.botId) {
            throw Error("Could not start meeting: no bot found for meeting.");
          }
          try {
            await new BotApi(configuration).botStartRecording({
              botUuid: res.botId,
            });
          } catch (e) {
            // Handle bot creation errors specifically, because we want to show a different message
            // depending on whether the bot was a video call bot or a phone call bot.
            logError(
              "app/routes/notes.create.($id)/route.tsx bot creation error",
              e
            );
            return data({ botCreationFailed: true }, { status: 400 });
          }
        }

        if (action === ActionTypes.SAVE_NOTE_AND_LEAVE_MEETING) {
          if (!res.botId) {
            throw Error("Could not end meeting: no bot found for meeting.");
          }
          await new BotApi(configuration).botLeaveMeeting({
            botUuid: res.botId,
          });
          return redirect(
            isUnifyAdvisorHubAndNotesEnabled ? "/dashboard" : "/notes"
          );
        }

        // Redirect to the hub if the caller explicitly asked for it
        if (action === ActionTypes.SAVE_NOTE_AND_REDIRECT_TO_HUB) {
          return redirect(
            isUnifyAdvisorHubAndNotesEnabled ? "/dashboard" : "/notes"
          );
        }

        // Redirect with the note ID in the URL parameters, and as the final path entry.
        // The note ID from the URL parameters is used to match this create page with a note.
        // The note ID in the URL path is used to match this create page with an entry in the notes
        // list in the sidebar.
        const url = new URL(request.url);
        url.searchParams.set("noteID", res.noteId);
        url.pathname = `/notes/create/${res.noteId}`;

        // Remove information derived from the calendar event from the URL (if any).
        url.searchParams.delete("meetingTitle");
        url.searchParams.delete("meetingLink");
        url.searchParams.delete("attendees");
        url.searchParams.delete("linkedCRMEntityID");
        url.searchParams.delete("linkedCRMEntityName");
        url.searchParams.delete("startTime");
        url.searchParams.delete("endTime");
        url.searchParams.delete("scheduledEventUUID");
        return redirect(url.toString());
      case ActionTypes.DELETE_NOTE:
        if (!noteID) {
          throw Error("Delete note action requested without a note ID");
        }
        await noteAPI.noteDeleteNote({ noteId: noteID });
        return redirect(
          isUnifyAdvisorHubAndNotesEnabled ? "/dashboard" : "/notes"
        );

      case ActionTypes.UPLOAD_AUDIO_CHUNK:
        if (!noteID) {
          throw Error("Upload audio chunk action requested without a note ID");
        }
        if (!createFormData.fileContent) {
          throw Error("Upload audio chunk action requested without a file");
        }
        if (createFormData.sequenceNumber === undefined) {
          throw Error(
            "Upload audio chunk action requested without a sequence number"
          );
        }
        if (createFormData.fragmentsNonce === undefined) {
          throw Error(
            "Upload audio chunk action requested without a fragment nonce"
          );
        }
        await noteAPI.noteUploadAudioChunk({
          audioData: createFormData.fileContent,
          noteId: noteID,
          sequence: createFormData.sequenceNumber,
          duration: fragmentDurationSeconds,
          mimeType: "audio/webm",
          nonce: createFormData.fragmentsNonce,
        });
        return data(
          { processedFragmentIndex: createFormData.sequenceNumber },
          { status: 200 }
        );
      default:
        throw new Error(`Unknown action: ${action}`);
    }
  } catch (e) {
    logError("app/routes/notes.create.($id)/route.tsx action error", e);
    const error = e instanceof Error ? e.message : "Something went wrong";
    return data({ error }, { status: 400 });
  }
};

// Meeting types that match the legacy meeting types hard coded into this app.
export const legacyMeetingTypes = [
  {
    uuid: "client",
    name: "Client",
    category: MeetingCategory.Client,
    isShared: true,
  },
  {
    uuid: "internal",
    name: "Internal",
    category: MeetingCategory.Internal,
    isShared: true,
  },
  {
    uuid: "debrief",
    name: "Debrief",
    category: MeetingCategory.Debrief,
    isShared: true,
  },
];

export const loader = async ({ request }: LoaderFunctionArgs) => {
  try {
    const attendeeOptions: AttendeeOptions = await getAttendeeOptions({
      request,
    });

    const { userId } = await getUserSessionOrRedirect(request);

    const searchParams = new URL(request.url).searchParams;

    // Load note details
    //
    // It's possible that the note data is both in the query params and the path. This component
    // uses the note in the query params as concerns the logic in this page that relies on a note.
    // The note in the query path is used by other components to match this create page with a note,
    // which may be a scheduled note that has been created before.
    const noteID = searchParams.get("noteID");
    const note = noteID
      ? await getNoteById({ noteId: noteID, request })
      : undefined;
    const configuration = new Configuration(
      await configurationParameters(request)
    );
    const bot = note?.botId
      ? await new BotApi(configuration).botGetBot({ botUuid: note.botId })
      : undefined;

    // Load interaction details
    const meetingArtifactsAPI = new MeetingArtifactsApi(configuration);
    const interactionId = note?.interactionUuid;
    const interactionPromise = interactionId
      ? meetingArtifactsAPI.meetingArtifactsGetClientInteraction({
          interactionUuid: interactionId,
        })
      : Promise.resolve(undefined);

    let eventSourceURL = undefined;
    if (bot && bot.platformId) {
      eventSourceURL = (
        await getEventStreamParams({ botID: bot.uuid, request })
      ).eventSourceURL;
    }

    const meetingTypesPromise = new MeetingArtifactsApi(
      configuration
    ).meetingArtifactsMeetingTypes();

    // Get attendees from the search parameters, if any.
    let prefilledAttendees: AttendeeOptions;
    try {
      const parsedAttendees = AttendeeOptionsStruct.safeParse(
        JSON.parse(safeAtob(searchParams.get("attendees") || ""))
      );
      prefilledAttendees = parsedAttendees.success ? parsedAttendees.data : [];
    } catch {
      prefilledAttendees = [];
    }

    const userAsAttendee = [
      attendeeOptions.find((item) => item.uuid === userId),
    ];

    const [interaction, meetingTypes] = await Promise.all([
      interactionPromise,
      meetingTypesPromise,
    ]);

    // Construct the lists of initial and selectable attendees.
    // If there is an existing note, use the attendees from the note as the initial attendees.
    // If not, use the attendees from the query params plus the user as the initial attendees.
    const {
      initialAttendees: initialAttendeesToUse,
      attendeeOptions: optionsToUse,
    } = resolvedAttendeeOptions(attendeeOptions, note?.attendees, [
      ...(note?.attendees === undefined ? userAsAttendee : []),
      ...prefilledAttendees,
    ]);

    return {
      note,
      bot,
      interaction,
      attendeeOptions: optionsToUse,
      initialAttendees: initialAttendeesToUse,
      userAsAttendee: userAsAttendee,
      userId,
      eventSourceURL,
      initialMeetingTitle:
        note?.meetingName || searchParams.get("meetingTitle") || undefined,
      initialMeetingLink:
        bot?.meetingLink || searchParams.get("meetingLink") || "",
      initialStartTime:
        note?.scheduledStartTime?.toISOString() ||
        searchParams.get("startTime") ||
        undefined,
      initialEndTime:
        note?.scheduledEndTime?.toISOString() ||
        searchParams.get("endTime") ||
        undefined,
      linkedCRMEntityID: searchParams.get("linkedCRMEntityID") || undefined,
      linkedCRMEntityName: searchParams.get("linkedCRMEntityName") || undefined,
      scheduledEventUUID: searchParams.get("scheduledEventUUID") || undefined,
      meetingTypes: meetingTypes,
    };
  } catch (e) {
    logError("auth.notes.create.($id) loader error", e);
    return {
      note: undefined,
      bot: undefined,
      interaction: undefined,
      attendeeOptions: [],
      initialAttendees: [],
      userId: "",
      eventSourceURL: undefined,
      initialMeetingTitle: undefined,
      initialMeetingLink: undefined,
      initialStartTime: undefined,
      initialEndTime: undefined,
      linkedCRMEntityID: undefined,
      linkedCRMEntityName: undefined,
      scheduledEventUUID: undefined,
      meetingTypes: {
        meetingTypes: [],
        defaultMeetingType: null,
      },
    };
  }
};

enum AudioSourceTypes {
  AudioUpload = "audio_upload",
  Mic = "mic",
  NoteTaker = "notetaker",
  PhoneCall = "phone_call",
}

const ReadonlyMeetingTime = ({ date }: { date: Date }) => (
  <Tooltip>
    <TooltipTrigger>
      <span className="cursor-help p-1">
        {date.toLocaleString(undefined, {
          dateStyle: "short",
          timeStyle: "short",
        })}
      </span>
    </TooltipTrigger>
    <TooltipContent>
      This meeting was imported from your calendar. If the meeting time is
      incorrect, please update it in your calendar.
    </TooltipContent>
  </Tooltip>
);

const Route = () => {
  const {
    note,
    bot,
    interaction,
    attendeeOptions: initialAttendeeOptions,
    initialAttendees,
    userId,
    eventSourceURL,
    initialMeetingTitle,
    initialMeetingLink,
    initialStartTime,
    initialEndTime,
    linkedCRMEntityID,
    linkedCRMEntityName,
    scheduledEventUUID,
    meetingTypes,
  } = useLoaderData<{
    note: NoteResponse | undefined;
    bot: Bot | undefined;
    interaction: ClientInteraction | undefined;
    attendeeOptions: AttendeeOptions;
    initialAttendees: AttendeeOptions;
    userId: string;
    eventSourceURL: string | undefined;
    initialMeetingTitle: string | undefined;
    initialMeetingLink: string;
    initialStartTime: string | undefined;
    initialEndTime: string | undefined;
    linkedCRMEntityID: string | undefined;
    linkedCRMEntityName: string | undefined;
    scheduledEventUUID: string | undefined;
    meetingTypes: MeetingTypesResponse;
  }>();
  const params = useParams();
  const meetingSourceID = params.id !== note?.uuid ? params.id : undefined;
  const navigation = useNavigation();
  const navigate = useNavigate();
  const location = useLocation();
  const submit = useSubmit();
  const saveNoteFetcher = useFetcher<{
    error?: string;
    botCreationFailed?: boolean;
    draftNoteID?: string;
  }>();
  const actionData = useActionData<{ error?: string }>();

  const [crmEntities, setCrmEntities] = useState<AttendeeOptions>([]); // this is for "CRM Records" in Meeting Prep modal

  const enablePhoneCallRecordings = useFlag("EnablePhoneCallRecordings");
  const enableMeetingPrep = useFlag(
    "EnableClientIntelligencePreMeetingWorkflow"
  );
  const enableAudioFileUploads = useFlag("EnableAudioFileUpload");

  const [searchParams] = useSearchParams();
  const tabFromSearchParams = searchParams.get("tab") ?? "record";
  const [meetingTab, setMeetingTab] = useState(
    enableMeetingPrep ? tabFromSearchParams : "record"
  );
  const updateMeetingTab = (meetingTab: string) => {
    setMeetingTab(meetingTab);
    searchParams.set("tab", meetingTab);
  };
  const [meetingLink, setMeetingLink] = useState<string>(
    bot?.meetingLink ?? initialMeetingLink
  );
  const botStatus = bot?.status;
  const [attendees, setAttendees] = useState<AttendeeOptions>(initialAttendees);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [touched, setTouched] = useState(false);
  const [isTitleUserEdited, setIsTitleUserEdited] = useState(false);

  const [isSavingAndRegenerating, setIsSavingAndRegenerating] = useState(false);
  const revalidator = useRevalidator();

  const [showGeneratePrepModal, setShowGeneratePrepModal] = useState(false);

  const isUnifyAdvisorHubAndNotesEnabled = useFlag(
    "EnableUnifyAdvisorHubAndNotes"
  );
  const { isMobile } = useUserAgent();

  // NOTE:
  // `EnableNewMeetingPrepFlow` flag is dependent on the existing `EnableClientIntelligencePreMeetingWorkflow` flag
  // Don't enable the former without the latter for any user / organization
  const isMeetingPrepRevampEnabled = !!useFlag("EnableNewMeetingPrepFlow");

  const attendeePhoneNumbers = attendees.flatMap((attendee) =>
    "phoneNumber" in attendee && attendee.phoneNumber
      ? [{ link: attendee.phoneNumber, name: attendee.name }]
      : []
  );
  const resolvedMeetingTypes = getResolvedMeetingTypes(
    meetingTypes,
    legacyMeetingTypes
  );
  const [meetingType, setMeetingType] = useState<SerializeFrom<MeetingType>>(
    resolvedMeetingTypes.find((t) => note?.meetingTypeUuid === t.uuid) ??
      resolvedMeetingTypes.find(
        (t) => meetingTypes.defaultMeetingType === t.uuid
      ) ??
      resolvedMeetingTypes[0]!
  );
  const [title, setTitle] = useState<string>(
    initialMeetingTitle ||
      getMeetingNameFromAttendees(userId, attendees, meetingType)
  );
  const [showTitleError, setShowTitleError] = useState<boolean>(false);
  const [startTime, setStartTime] = useState<Date>(
    initialStartTime ? new Date(initialStartTime) : new Date()
  );
  const [endTime, setEndTime] = useState<Date>(
    initialEndTime ? new Date(initialEndTime) : addHours(startTime, 1)
  );
  const timesEditable = note?.timesEditable ?? !meetingSourceID;

  // Chunked audio upload

  // The nonce that is attached to fragments during a recording session. If the user resets the
  // audio, this will reset.
  const [fragmentsNonce, setFragmentsNonce] = useState(Date.now());

  // The fragments that need to be processed by the server.
  const [fragmentsToProcess, setFragmentsToProcess] = useState<
    Record<string, Blob>
  >({});

  // A counter that tracks the number of chunked audio fragments that have been delivered.
  //
  // This is used to provide a stable state that only changes when fragments are added for
  // processing. `fragmentsToProcess` is an object and is updated when fragments are processed,
  // which makes it unsuitable for use in a useEffect dependency array.
  const [fragmentCount, setFragmentCount] = useState(0);

  // The indexes of fragments that are currently being processed.
  const [inProcessFragments, setInProcessFragments] = useState<string[]>([]);

  const {
    isRecording,
    isPaused,
    recordingBlob,
    stopRecordingWithWakeLock,
    recordingFragments,
  } = useContext(RecorderContext);

  // The ID for the note to use for uploading audio fragment data.
  //
  // Bias towards the ID of the note as derived from the URL parameters; fall back to the draft note
  // ID created when starting recording if that's not available. We assume that the parameter-derived
  // note ID is more authoritative than the draft note ID; and, if we navigate from one create page to
  // another, the draft note ID may be stale.
  const fragmentNoteID = note?.uuid ?? saveNoteFetcher?.data?.draftNoteID;

  // Add fragments for processing as they are generated.
  useEffect(() => {
    const lastFragmentIndex = recordingFragments.length - 1;
    const lastFragment = recordingFragments[lastFragmentIndex];
    if (!lastFragment) {
      setFragmentsToProcess({});
      return;
    }
    setFragmentsToProcess((prev) => {
      return {
        ...prev,
        [lastFragmentIndex]: lastFragment,
      };
    });
    // Make sure this is set after the fragmentsToProcess update above, so that the effect that
    // triggers next will have correct data.
    setFragmentCount(lastFragmentIndex + 1);
  }, [recordingFragments]);

  // Process fragments as needed.
  useEffect(() => {
    // Upload one fragment that is not already being processed. If this succeeds and there are
    // multiple fragments to process, the next fragment will be uploaded in the next render cycle.
    const [index, fragment] =
      Object.entries(fragmentsToProcess).filter(
        ([i, _]) => !inProcessFragments.includes(i)
      )[0] ?? [];
    if (!index || !fragment) {
      return;
    }

    if (!navigator.onLine) {
      // If the user is offline, don't upload the fragment. This will be retried later, when another
      // fragment is delivered.
      return;
    }

    const formData = new FormData();
    formData.append("action", ActionTypes.UPLOAD_AUDIO_CHUNK);
    formData.append("fileContent", fragment);
    formData.append("sequenceNumber", index);
    formData.append("fragmentsNonce", fragmentsNonce.toString());
    const noteUUID = fragmentNoteID;
    if (!noteUUID) {
      datadogLogs.logger.error(
        "app/routes/_auth.notes.create.($id)/route.tsx audio fragment upload error: noteUUID required to upload fragment"
      );
      return;
    }

    // Otherwise, use the fetch API to upload the fragment.
    // This avoids an issue with Remix where, if the fetch request made by the fetcher or the
    // loads that run in response are interupted, Remix will not pass the error to this route to
    // handle but will instead show the top-level error boundary.
    setInProcessFragments((prev) => [...prev, index]);
    const url = `/feapi/notes/create?noteID=${noteUUID}`;
    fetch(url, { method: "post", body: formData })
      .catch((e) => {
        datadogLogs.logger.error(
          "app/routes/_auth.notes.create.($id)/route.tsx audio fragment upload error",
          e
        );
      })
      .then(async (res) => {
        if (!res || !res.ok) {
          return;
        }
        try {
          const data = await res.json();
          const processedFragmentIndex = data.processedFragmentIndex as number;
          setFragmentsToProcess((prev) => {
            const newFragmentsToProcess = { ...prev };
            delete newFragmentsToProcess[processedFragmentIndex];
            return newFragmentsToProcess;
          });
        } catch (e) {
          datadogLogs.logger.error(
            `app/routes/_auth.notes.create.($id)/route.tsx audio fragment upload JSON parsing error: ${e}`
          );
        }
      })
      .finally(() => {
        setInProcessFragments((prev) => prev.filter((i) => i !== index));
      });
    // Leave out inProcessFragments and fragmentsToProcess; we don't want this effect to run every
    // time we change the current processing state. Instead, we rely on fragmentCount to trigger
    // re-evaluation when new fragments are added.
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fragmentCount, fragmentsNonce, note?.uuid, fragmentNoteID]);

  const showPhoneCallTabForBot =
    enablePhoneCallRecordings && meetingLink
      ? isValidPhoneNumber(meetingLink, "US")
      : false;
  const [selectedAudioTab, setSelectedAudioTab] = useState<string>(
    meetingLink
      ? showPhoneCallTabForBot
        ? AudioSourceTypes.PhoneCall
        : AudioSourceTypes.NoteTaker
      : AudioSourceTypes.Mic
  );
  const toastId = useRef<Id | null>(null);
  const [spinnerCTA, setSpinnerCTA] = useState("");

  const [
    notetakerControlsSectionCollapsed,
    setNotetakerControlsSectionCollapsed,
  ] = useState(false);
  const [
    shouldCollapseMeetingPrepSection,
    setShouldCollapseMeetingPrepSection,
  ] = useState(false);

  const { isTutorialEnabled, completeTutorial } = useOnboarding(
    "meeting-prep",
    { triggerViaUrl: true }
  );
  const introRef = useRef<Steps>(null);

  // in case onboarding experience is underway, show the "Use Notetaker" audio source
  useEffect(() => {
    if (isTutorialEnabled) {
      setSelectedAudioTab(AudioSourceTypes.NoteTaker);
    }
  }, [isTutorialEnabled]);

  const openDeleteModal = () => setIsDeleteModalOpen(true);
  const closeDeleteModal = () => setIsDeleteModalOpen(false);

  const confirmDelete = () => {
    handleDelete();
    closeDeleteModal();
  };

  const handleTitleBlur = () => {
    if (!isValidTitle(title)) {
      setShowTitleError(true);
    }
  };

  const handleTitleChange = (value: string) => {
    setShowTitleError(false);
    setTouched(true);
    setIsTitleUserEdited(true);
    setTitle(value);
  };

  // This isn't quite ideal–it would be nice to only set up the event source if the URL were truthy,
  // but that breaks React's requirement that hooks be called in the same order. The code here will
  // error when the URL is empty (and when the flag is disabled on the server), but the flow still
  // works.
  const streamedBotStatus = useEventSource(
    eventSourceURL || ""
  ) as BotStatus | null;
  const unifiedBotStatus = streamedBotStatus ?? botStatus;

  const botUUID = bot?.uuid ?? "";
  const isVideoMeeting = bot?.meetingType === BotMeetingType.VideoCall;
  // Show a toast if the bot is in the waiting room, or was not created.
  useEffect(() => {
    if (unifiedBotStatus == BotStatus.InWaitingRoom && isVideoMeeting) {
      toastId.current = toast.info("Check the waiting room!", {
        toastId: "bot-in-waiting-room",
      });
    }
  }, [unifiedBotStatus, botUUID, isVideoMeeting]);

  // Update the title based on the attendees and meeting type, barring edge cases
  useEffect(() => {
    // exit if title was provided via query params or note title
    if (initialMeetingTitle) {
      return;
    }

    // exit if title was edited by user
    if (isTitleUserEdited) {
      return;
    }

    setTitle(getMeetingNameFromAttendees(userId, attendees, meetingType));
  }, [initialMeetingTitle, isTitleUserEdited, userId, attendees, meetingType]);

  const saveNote = useCallback(
    (
      action: ActionTypes = ActionTypes.SAVE_NOTE,
      fileContent: Blob | null = null
    ) => {
      const formData = new FormData();
      formData.append("action", action);
      formData.append("meetingName", title);
      formData.append("meetingType", meetingType.uuid);
      formData.append("meetingLink", meetingLink);
      formData.append("clientIds", crmEntities.map((c) => c.uuid).join(","));
      if (fileContent) {
        formData.append("fileContent", fileContent);
        formData.append("fileType", fileContent.type);
      }
      if (meetingSourceID) {
        formData.append("meetingSourceID", meetingSourceID);
      }
      if (!scheduledEventUUID) {
        formData.append("startTime", startTime.toISOString());
        formData.append("endTime", endTime.toISOString());
      }
      if (scheduledEventUUID) {
        formData.append("scheduledEventUUID", scheduledEventUUID);
      }
      if (linkedCRMEntityID && linkedCRMEntityName) {
        formData.append("linkedCRMEntityID", linkedCRMEntityID);
        formData.append("linkedCRMEntityName", linkedCRMEntityName);
      }
      formData.append(
        "attendees",
        JSON.stringify(
          attendees.map((item) => ({
            name: item.name,
            type: item.type,
            uuid: item.uuid,
          }))
        )
      );

      setTouched(false);

      // Don't trigger a navigation if we are uploading audio data or creating a draft note. This is
      // a workaround for the fact that the hook that shows a dialog when the user tries to navigate
      // away from the page while there are unsaved changes is triggered before the updated value of
      // `touched` is propagated, and for the fact that the note created during the chunked upload flow
      // should not trigger a navigation.
      //
      // This is a hackish fix. We should find a better way to handle this.
      const noteUUID = note?.uuid ?? fragmentNoteID;
      saveNoteFetcher.submit(formData, {
        action: noteUUID
          ? `/notes/create/${noteUUID}?noteID=${noteUUID}`
          : "/notes/create",
        method: "post",
        encType: "multipart/form-data",
      });
    },
    [
      title,
      meetingType.uuid,
      meetingLink,
      crmEntities,
      meetingSourceID,
      scheduledEventUUID,
      linkedCRMEntityID,
      linkedCRMEntityName,
      attendees,
      note?.uuid,
      fragmentNoteID,
      saveNoteFetcher,
      startTime,
      endTime,
    ]
  );

  // If a note is finalized or processed, it is not editable, and we want to be able to navigate
  // away from the page without triggering a warning about unsaved changes.
  useEffect(() => {
    if (
      note?.status === ProcessingStatus.Finalized ||
      note?.status === ProcessingStatus.Processed
    ) {
      setTouched(false);
      return;
    }
  }, [note?.status]);

  // When a bot is ended or the recording is done, save the note and redirect to the hub/notes list.
  useEffect(() => {
    if (
      unifiedBotStatus == BotStatus.CallEnded ||
      unifiedBotStatus == BotStatus.RecordingDone
    ) {
      saveNote(ActionTypes.SAVE_NOTE_AND_REDIRECT_TO_HUB);
    }
    // This effect shouldn't be called if saveNote changes (which is not unlikely, since it has an
    // extensive dependency array), only when the bot status changes.
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [unifiedBotStatus]);

  // Show a toast if there is an error posting the form data. The form is posted for several
  // operations, so there are several different errors that may be displayed here.
  useEffect(() => {
    if (toastId.current && toast.isActive(toastId.current)) {
      return;
    }

    // This isn't a perfect way to track the error: it's possible that the error is from a previous
    // fetch operation and hasn't yet been cleared. We should revisit this.
    const error = actionData?.error ?? saveNoteFetcher.data?.error;
    if (error) {
      toastId.current = toast.error(error, { toastId: "notes-create-error" });
      // If there was an error with the action, assume that something was changed that has not yet
      // been persisted. This isn't a perfectly-correct assumption, but it helps prevent the user
      // from losing data (at the expense of potentially an unnecessary warning dialog upon leaving
      // the page).
      setTouched(true);
      return;
    }

    // Handle bot creation errors specifically, because we want to show a different message for
    // video bots versus phone call bots.
    if (saveNoteFetcher.data?.botCreationFailed ?? false) {
      toastId.current = toast.error(
        isVideoMeeting
          ? "Could not send Notetaker to your meeting. Please check the meeting link and try again."
          : "Could not connect or record the phone call. Please check the phone number and try again.",
        {
          toastId: "bot-not-created",
        }
      );
      return;
    }
  }, [
    actionData,
    saveNoteFetcher.data?.botCreationFailed,
    saveNoteFetcher.data?.error,
    isVideoMeeting,
  ]);

  // `recordingBlob` becomes available once a recording is _fully stopped_. As
  // soon as it is available, trigger form submission via this hook.
  useEffect(() => {
    if (!recordingBlob || !navigator.onLine) {
      return;
    }
    saveNote(ActionTypes.SAVE_NOTE, recordingBlob);
    // This shouldn't be called if saveNote changes; otherwise, each time the note is uploaded and
    // the fetcher state changes, this effect will re-trigger in a loop.
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [recordingBlob]);

  // trigger "generate meeting prep" flow if action is "meeting-prep" & if there is no "interaction"
  useEffect(() => {
    if (!interaction && searchParams.get("action") === "meeting-prep") {
      openGeneratePrepModal();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useBeforeUnload(
    useCallback(
      (event) => {
        if (!touched) {
          return;
        }
        event.preventDefault();
        event.returnValue = "Discard unsaved changes?";
      },
      [touched]
    )
  );

  unstable_usePrompt({
    message: "Discard unsaved changes?",
    when: ({ currentLocation, nextLocation }) => {
      return touched && currentLocation.pathname !== nextLocation.pathname;
    },
  });

  const handleDelete = () => {
    const formData = new FormData();
    formData.append("action", ActionTypes.DELETE_NOTE);
    submit(formData, {
      action: note?.uuid
        ? `/notes/create/${note.uuid}?noteID=${note.uuid}`
        : "/notes/create",
      method: "post",
      encType: "multipart/form-data",
    });
  };

  const saveDisabledBotStatuses = new Set<BotStatus>([
    BotStatus.InWaitingRoom,
    BotStatus.InCallNotRecording,
    BotStatus.InCallRecording,
  ]);

  // When there is a bot, check if it is in a state that should cause the save button to be
  // disabled. We don't want users to be able to save bot meetings, because it causes confusion.
  const shouldDisableForBotMeeting =
    unifiedBotStatus && saveDisabledBotStatuses.has(unifiedBotStatus);

  // The save button should be enabled when:
  // - the title is valid
  // - the form is not submitting
  // - if this is a bot meeting, the bot is not in a state where saving is disabled
  //
  // In the case of a meeting with both bot and mic audio, the save button should track the state of
  // the mic audio; we assume that, if the user has started a mic session after a bot, they want the
  // mic audio. We can more easily recover lost bot audio than mic audio that was never uploaded.
  const enableSaveNoteButton =
    isValidTitle(title) &&
    navigation.state === "idle" &&
    saveNoteFetcher.state === "idle" &&
    !shouldDisableForBotMeeting;

  const isLoading =
    navigation.state !== "idle" || saveNoteFetcher.state !== "idle";

  const onCompleteTutorial = () => {
    completeTutorial();
    navigate("/notes", { replace: true });
  };

  const onExitTutorial = (stepNumber: number) => {
    // check to avoid the issue (potentially with React wrapper itself) where onExitTutorial is called automatically at the beginning
    if (stepNumber >= 0) {
      onCompleteTutorial();
    }
  };

  // handle dynamic elements - check if element exists and make IntroJS get the updated element
  const handleBeforeChange = async (nextStepIndex: number) => {
    const { hasDynamicElement, element } = tutorialSteps[nextStepIndex] || {};
    if (hasDynamicElement && element) {
      const targetElement = document.querySelector(element);
      if (targetElement) {
        introRef.current?.updateStepElement(nextStepIndex);
      }
    }
  };

  const saveAndRegenerate = async () => {
    // exit if noteId isn't available; or if we're already saving and regenerating
    if (!note?.uuid || isSavingAndRegenerating) {
      return;
    }

    setIsSavingAndRegenerating(true);

    // save the note first
    const { success: saveSuccess, error } = await fetchPost(
      "/feapi/notes/create-or-update",
      {
        noteId: note.uuid,
        meetingName: title,
        meetingLink,
        meetingSourceId: meetingSourceID,
        scheduledStartTime: startTime,
        scheduledEndTime: endTime,
        meetingType: meetingType.uuid,
        attendees,
      }
    );

    // handle save failures, and exit
    if (!saveSuccess) {
      toast.error("Failed to save note. Please try again.", {
        autoClose: 2000,
      });
      setIsSavingAndRegenerating(false);
      return;
    }

    // mark the note as not touched, to avoid the unsaved changes dialog
    setTouched(false);

    // regenerate the meeting prep
    const { success } = await fetchPost("/feapi/notes/generate-meeting-prep", {
      noteId: note.uuid,
      clientIds: crmEntities.map((c) => c.uuid),
    });

    if (success) {
      toast.success("Meeting prep generation in progress", {
        autoClose: 2000,
      });
    } else {
      toast.error("Failed to generate meeting prep. Please try again.", {
        autoClose: 2000,
      });
    }

    setIsSavingAndRegenerating(false);

    // close modal
    setShowGeneratePrepModal(false);

    // ensure toast is displayed before revalidating
    setTimeout(() => {
      revalidator.revalidate();
    }, 0);
  };

  const openGeneratePrepModal = () => {
    setShowGeneratePrepModal(true);
  };

  const agendaData = followUpDataForFollowUp(interaction?.agenda);
  const advisorNotesData = followUpDataForFollowUp(interaction?.advisorNotes);

  const agendaDataIsValid =
    agendaData &&
    "content" in agendaData.parsedData &&
    agendaData.followUp.status === FollowUpStatus.Completed;
  const advisorNotesDataIsValid =
    advisorNotesData &&
    "content" in advisorNotesData.parsedData &&
    advisorNotesData.followUp.status === FollowUpStatus.Completed;

  const hasSelectedUnknownAttendees = attendees.some(
    (a) => a.type === "unknown"
  );

  const actionButtons = (
    <>
      {note && <DeleteButton onClick={openDeleteModal} tooltip="Delete Note" />}

      {isMeetingPrepRevampEnabled && enableMeetingPrep && (
        <Tooltip>
          <TooltipTrigger>
            {note ? (
              <Button
                variant={isMobile ? "ghost" : "outline_primary"}
                className="ml-auto"
                disabled={
                  !agendaDataIsValid ||
                  !advisorNotesDataIsValid ||
                  isSavingAndRegenerating
                }
                onClick={openGeneratePrepModal}
              >
                <Sparkles />
                Regenerate Meeting Prep
                {isSavingAndRegenerating && <Spinner />}
              </Button>
            ) : (
              <Button
                variant={isMobile ? "ghost" : "outline_primary"}
                className="ml-auto"
                onClick={openGeneratePrepModal}
              >
                <Sparkles />
                Generate Meeting Prep
              </Button>
            )}
          </TooltipTrigger>

          <TooltipContent>
            Save and {note ? "Regenerate" : "Generate"} Meeting Prep
          </TooltipContent>
        </Tooltip>
      )}
    </>
  );

  return (
    <SidebarV2
      favorSidebarOnMobile
      header={
        <HeaderV2
          className="px-0 md:px-5"
          title="Create a note"
          subtitle="Create a note"
          left={
            !isUnifyAdvisorHubAndNotesEnabled && (
              <BackButton
                to={location.state?.from ?? "/notes"}
                tooltip="Back"
              />
            )
          }
          right={
            <div className="flex flex-row gap-2">
              {/* TODO: @debojyotighosh Once `isTablet` is added, render all buttons for tablet */}
              {/* render ... button for mobile, and all buttons for desktop */}
              {isMobile ? (
                <ActionButtonDrawer>{actionButtons}</ActionButtonDrawer>
              ) : (
                actionButtons
              )}

              <SaveButton
                disabled={!enableSaveNoteButton}
                loading={isLoading}
                tooltip={
                  unifiedBotStatus && shouldDisableForBotMeeting
                    ? "The note will save automatically after the meeting ends."
                    : isRecording
                      ? "End meeting"
                      : "Save"
                }
                title={isRecording ? "End meeting" : "Save"}
                onClick={() => {
                  const isBotMeeting =
                    selectedAudioTab === AudioSourceTypes.NoteTaker ||
                    selectedAudioTab === AudioSourceTypes.PhoneCall;

                  // If the user is offline, warn them that the operation cannot be completed.
                  if (!navigator.onLine) {
                    toast.error(
                      "You are offline. Please check your connection."
                    );
                    return;
                  }

                  if (!isRecording) {
                    saveNote(ActionTypes.SAVE_NOTE);
                    return;
                  }

                  // If this is a bot meeting, then save the note accordingly.
                  if (isBotMeeting) {
                    saveNote(ActionTypes.SAVE_NOTE);
                  } else {
                    // The recorder library is weird. It only gives access to the recording blob
                    // after recording has been stopped. This means that stopping recording is
                    // equivalent to saving the note. It's all weirdly inverted, so we have to do
                    // some gymnastics. First, we stop recording here. That causes `recordingBlob`
                    // to become available via RecorderContext. We then finally submit the form via
                    // a `useEffect`.
                    stopRecordingWithWakeLock();
                  }
                }}
              />
            </div>
          }
        />
      }
    >
      <Steps
        enabled={isTutorialEnabled}
        steps={tutorialSteps}
        initialStep={0}
        onExit={onExitTutorial}
        onComplete={onCompleteTutorial}
        options={{
          exitOnOverlayClick: false,
        }}
        onBeforeChange={handleBeforeChange}
        ref={introRef}
      />

      <div className="flex flex-col gap-3 self-stretch md:px-5">
        <div className="flex w-full flex-col py-2">
          <input
            value={title}
            autoFocus
            onBlur={handleTitleBlur}
            onChange={(event) => handleTitleChange(event.currentTarget.value)}
            placeholder="Add a note title"
            className={cn(
              "w-full border-none text-3xl font-semibold focus:outline-none",
              showTitleError ? "text-red-600" : "text-black"
            )}
          />
          {showTitleError && (
            <Typography variant="body2" color="error">
              A note title is required
            </Typography>
          )}
        </div>
        <AfterHydration>
          <Typography
            className="inline-flex items-center rounded-md"
            data-onboarding="scheduled-timestamp"
          >
            <Calendar />
            <span className="ml-1 mr-2 w-[75px]">Start time</span>
            <span>
              {timesEditable ? (
                <input
                  className={"border-1 rounded-md border border-border p-1"}
                  aria-label="Meeting start date and time"
                  type="datetime-local"
                  min={format(new Date(), "yyyy-MM-dd'T'HH:mm")}
                  value={format(startTime, "yyyy-MM-dd'T'HH:mm")}
                  onChange={(e) => {
                    if (!e.target.validity.valid) {
                      return;
                    }
                    if (!e.target.value) {
                      return;
                    }
                    const localDate = new Date(e.target.value);
                    if (localDate > endTime) {
                      setEndTime(localDate);
                    }
                    setStartTime(localDate);
                    setTouched(true);
                  }}
                />
              ) : (
                <ReadonlyMeetingTime date={startTime} />
              )}
            </span>
          </Typography>
          <Typography className="inline-flex items-center rounded-md">
            <Calendar />
            <span className="ml-1 mr-2 w-[75px]">End time</span>
            <span>
              {timesEditable ? (
                <input
                  className={"border-1 rounded-md border border-border p-1"}
                  aria-label="Meeting end date and time"
                  type="datetime-local"
                  min={format(new Date(), "yyyy-MM-dd'T'HH:mm")}
                  value={format(endTime, "yyyy-MM-dd'T'HH:mm")}
                  onChange={(e) => {
                    if (!e.target.validity.valid) {
                      return;
                    }
                    if (!e.target.value) {
                      return;
                    }
                    const localDate = new Date(e.target.value);
                    if (localDate < startTime) {
                      setStartTime(localDate);
                    }
                    setEndTime(localDate);
                    setTouched(true);
                  }}
                />
              ) : (
                <ReadonlyMeetingTime date={endTime} />
              )}
            </span>
          </Typography>
        </AfterHydration>

        {isMeetingPrepRevampEnabled && (
          <MeetingTypeSelector
            meetingType={meetingType}
            setMeetingType={(type) => {
              setTouched(true);
              setMeetingType(type);
            }}
            meetingTypes={resolvedMeetingTypes}
          />
        )}

        <Separator className="my-2" />

        {enableMeetingPrep && !isMeetingPrepRevampEnabled && (
          <ToggleGroup
            className="scollbar-hidden w-full justify-start overflow-auto p-0"
            data-onboarding="meeting-tabs"
            value={meetingTab}
            type="single"
            onValueChange={(value) => {
              if (!value) {
                return;
              }
              updateMeetingTab(value);
            }}
          >
            <ToggleGroupItem value="record" aria-label="Show Meeting Details">
              Meeting Details
            </ToggleGroupItem>
            <ToggleGroupItem
              value="prep"
              aria-label="Show Meeting Prep"
              // Don't allow the user to generate prep if they have mic audio.
              //
              // This is a quick fix for https://zeplyn.atlassian.net/browse/ENG-1089.
              disabled={!interaction && isRecording}
            >
              Meeting Prep
            </ToggleGroupItem>
          </ToggleGroup>
        )}

        <div
          className={cn(
            "flex items-start gap-2",
            isMeetingPrepRevampEnabled && "flex-col pb-8 lg:pb-0 xl:flex-row"
          )}
        >
          {/* Notetaker Controls */}
          <div
            className={cn(
              "flex flex-col items-start gap-2",
              enableMeetingPrep &&
                meetingTab === "prep" &&
                !isMeetingPrepRevampEnabled &&
                "hidden",
              isMeetingPrepRevampEnabled &&
                (enableMeetingPrep
                  ? "w-full shrink-0 xl:w-1/3 xl:min-w-[400px]"
                  : "w-full")
            )}
          >
            {isMeetingPrepRevampEnabled && (
              <h3 className="mb-2 flex w-full border-l-4 px-2 py-1 text-lg font-semibold text-muted-foreground">
                Notetaker Controls
                <span
                  className="ml-auto shrink-0 cursor-pointer xl:hidden"
                  onClick={() =>
                    setNotetakerControlsSectionCollapsed(
                      !notetakerControlsSectionCollapsed
                    )
                  }
                >
                  {notetakerControlsSectionCollapsed ? (
                    <ChevronDown />
                  ) : (
                    <ChevronUp />
                  )}
                </span>
              </h3>
            )}

            {!notetakerControlsSectionCollapsed && (
              <>
                {!isMeetingPrepRevampEnabled && (
                  <MeetingTypeSelector
                    meetingType={meetingType}
                    setMeetingType={(type) => {
                      setTouched(true);
                      setMeetingType(type);
                    }}
                    meetingTypes={resolvedMeetingTypes}
                  />
                )}

                <FormField
                  id="attendees"
                  name="attendees"
                  className="max-w-full"
                >
                  <FormLabel>
                    {meetingType.category === "debrief"
                      ? "Clients"
                      : "Attendees"}
                  </FormLabel>
                  <FormControl>
                    <AttendeesMultiSelect
                      placeholder={
                        meetingType.category === "debrief"
                          ? "List clients"
                          : "List attendees"
                      }
                      emptyLabel="Start typing to add new attendee."
                      leftIcon={<Users className="shrink-0" />}
                      initialOptions={initialAttendeeOptions}
                      selected={attendees}
                      onChange={(nextAttendees) => {
                        setAttendees(nextAttendees);
                        setTouched(true);
                      }}
                      triggerClassName="w-full"
                      commandClassName="w-full"
                      maxListHeightPixels={260}
                    />
                  </FormControl>
                  <FormDescription>
                    {hasSelectedUnknownAttendees && (
                      <div className="mb-1 rounded-sm border border-muted p-2">
                        <div className="mb-1 flex items-center gap-1 font-medium">
                          <Lightbulb size={15} className="shrink-0" />
                          Quick Tip
                        </div>
                        <div>
                          You’ve added a new attendee. Click their gray tag to
                          create a client profile.
                        </div>
                      </div>
                    )}
                    Listing other attendees and clients is highly recommended
                  </FormDescription>
                </FormField>
                <div data-onboarding="audio-source">
                  <TooltipProvider>
                    <Typography variant="body3">
                      {isMeetingPrepRevampEnabled
                        ? "Meeting Location"
                        : "Audio Source"}
                    </Typography>
                    <Tabs
                      value={selectedAudioTab}
                      className="space-y-4"
                      onValueChange={setSelectedAudioTab}
                    >
                      <TabsList>
                        {/* TODO: @debojyotighosh Nested buttons error in /notes/create/$id page */}
                        <Tooltip>
                          <TooltipTrigger>
                            <TabsTrigger
                              value={AudioSourceTypes.Mic}
                              disabled={isRecording || isPaused}
                            >
                              {isMeetingPrepRevampEnabled
                                ? "In-person"
                                : "Use Mic"}
                            </TabsTrigger>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>
                              Use your microphone for note-taking. If using this
                              option for video meetings, you shouldn't wear ear
                              phones
                            </p>
                          </TooltipContent>
                        </Tooltip>
                        <Tooltip>
                          <TooltipTrigger>
                            <TabsTrigger
                              value={AudioSourceTypes.NoteTaker}
                              disabled={isRecording || isPaused}
                            >
                              {isMeetingPrepRevampEnabled
                                ? "Virtual"
                                : "Use Notetaker"}
                            </TabsTrigger>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>
                              Send the notetaker to your video meeting to take
                              notes
                            </p>
                          </TooltipContent>
                        </Tooltip>
                        {enablePhoneCallRecordings && (
                          <Tooltip>
                            <TooltipTrigger>
                              <TabsTrigger
                                value={AudioSourceTypes.PhoneCall}
                                disabled={isRecording || isPaused}
                              >
                                {isMeetingPrepRevampEnabled
                                  ? "Phone Call"
                                  : "Call a Phone"}
                              </TabsTrigger>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>
                                Send the notetaker to a phone call to take notes
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        )}
                        {enableAudioFileUploads && (
                          <Tooltip>
                            <TooltipTrigger>
                              <TabsTrigger
                                value={AudioSourceTypes.AudioUpload}
                                disabled={isRecording || isPaused}
                              >
                                Upload Audio File
                              </TabsTrigger>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Upload an audio file to take notes</p>
                            </TooltipContent>
                          </Tooltip>
                        )}
                      </TabsList>
                    </Tabs>
                  </TooltipProvider>
                </div>

                {enableAudioFileUploads &&
                  selectedAudioTab === AudioSourceTypes.AudioUpload && (
                    <AudioFileUploader
                      needConsent={meetingType.category === "client"}
                      setTouched={setTouched}
                      onFileUploaded={(file) => {
                        saveNote(ActionTypes.SAVE_NOTE, file);
                      }}
                    />
                  )}
                {selectedAudioTab === AudioSourceTypes.Mic && (
                  <RecorderCard
                    onStartRecording={() => {
                      setTouched(true);
                      setFragmentsNonce(Date.now());
                      if (note?.uuid ?? fragmentNoteID) {
                        return;
                      }
                      saveNote(ActionTypes.CREATE_DRAFT_NOTE);
                      // Ensure that the note remains in a dirty state so that page refreshes and
                      // navigations don't lose mic audio data.
                      setTouched(true);
                    }}
                    needConsent={meetingType.category === "client"}
                    fragmentMilliseconds={fragmentDurationSeconds * 1000}
                  />
                )}
                {selectedAudioTab === AudioSourceTypes.NoteTaker && (
                  <NoteTakerController
                    botId={note?.botId ?? ""}
                    botStatus={unifiedBotStatus}
                    meetingLink={meetingLink}
                    setMeetingLink={setMeetingLink}
                    meetingLinkSuggestions={[]}
                    onTouched={() => setTouched(true)}
                    needConsent={
                      !isTutorialEnabled && meetingType.category === "client"
                    }
                    sendBotAndCreateNote={() =>
                      saveNote(ActionTypes.SAVE_NOTE_AND_START_MEETING)
                    }
                    saveNoteTaking={() =>
                      saveNote(ActionTypes.SAVE_NOTE_AND_LEAVE_MEETING)
                    }
                    spinnerCTA={spinnerCTA}
                    setSpinnerCTA={setSpinnerCTA}
                    supportsPauseResume={bot?.supportsPauseResume ?? true}
                    botType={BotMeetingType.VideoCall}
                    botOperationLoading={isLoading}
                  />
                )}
                {enablePhoneCallRecordings &&
                  selectedAudioTab === AudioSourceTypes.PhoneCall && (
                    <NoteTakerController
                      botId={note?.botId ?? ""}
                      botStatus={unifiedBotStatus}
                      meetingLink={meetingLink}
                      meetingLinkSuggestions={attendeePhoneNumbers}
                      setMeetingLink={setMeetingLink}
                      onTouched={() => setTouched(true)}
                      needConsent={meetingType.category === "client"}
                      sendBotAndCreateNote={() =>
                        saveNote(ActionTypes.SAVE_NOTE_AND_START_MEETING)
                      }
                      saveNoteTaking={() =>
                        saveNote(ActionTypes.SAVE_NOTE_AND_LEAVE_MEETING)
                      }
                      spinnerCTA={spinnerCTA}
                      setSpinnerCTA={setSpinnerCTA}
                      supportsPauseResume={bot?.supportsPauseResume ?? true}
                      botType={BotMeetingType.PhoneCall}
                      botOperationLoading={isLoading}
                    />
                  )}
              </>
            )}
          </div>

          {isMeetingPrepRevampEnabled && enableMeetingPrep && (
            <Separator orientation="vertical" />
          )}

          {/* Meeting Prep */}
          {enableMeetingPrep && (
            <div
              className={cn(
                "flex grow flex-col gap-2",
                meetingTab === "prep" || isMeetingPrepRevampEnabled
                  ? null
                  : "hidden",
                isMobile && "w-full"
              )}
            >
              {isMeetingPrepRevampEnabled && (
                <h3 className="mb-2 flex w-full border-l-4 px-2 py-1 text-lg font-semibold text-muted-foreground">
                  Meeting Prep
                  <span
                    className="ml-auto shrink-0 cursor-pointer xl:hidden"
                    onClick={() =>
                      setShouldCollapseMeetingPrepSection(
                        !shouldCollapseMeetingPrepSection
                      )
                    }
                  >
                    {shouldCollapseMeetingPrepSection ? (
                      <ChevronDown />
                    ) : (
                      <ChevronUp />
                    )}
                  </span>
                </h3>
              )}

              {!shouldCollapseMeetingPrepSection && (
                <>
                  <MeetingPrepTab
                    interaction={interaction}
                    clients={attendees.filter((a) => a.type === "client")}
                    readOnly={false}
                    isSavingAndRegenerating={isSavingAndRegenerating}
                    isMeetingPrepRevampEnabled={isMeetingPrepRevampEnabled}
                    openPrepModal={openGeneratePrepModal}
                  />

                  <MeetingPrepModal
                    isOpen={
                      isMeetingPrepRevampEnabled
                        ? showGeneratePrepModal
                        : meetingTab == "prep" && !interaction
                    }
                    onClose={() => {
                      isMeetingPrepRevampEnabled
                        ? setShowGeneratePrepModal(false)
                        : updateMeetingTab("record");
                    }}
                    meetingTypes={resolvedMeetingTypes}
                    meetingType={meetingType}
                    setMeetingType={setMeetingType}
                    onSave={() => {
                      interaction
                        ? saveAndRegenerate()
                        : saveNote(ActionTypes.SAVE_NOTE);
                    }}
                    interaction={interaction}
                    isLoading={isLoading}
                    isMeetingPrepRevampEnabled={isMeetingPrepRevampEnabled}
                    selectedAttendees={attendees}
                    allClients={initialAttendeeOptions.filter(
                      (a) => a.type === "client"
                    )}
                    crmEntities={crmEntities}
                    setCrmEntities={setCrmEntities}
                  />
                </>
              )}
            </div>
          )}
        </div>
      </div>
      <ConfirmModal
        isOpen={isDeleteModalOpen}
        onClose={closeDeleteModal}
        onConfirm={confirmDelete}
        title="Confirm Delete"
        message="Are you sure you want to delete this note? This will delete the note for all users it's shared with."
      />
    </SidebarV2>
  );
};

export default () => (
  <RecorderProvider>
    <Route />
  </RecorderProvider>
);
