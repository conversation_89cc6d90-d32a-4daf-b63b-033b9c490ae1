import { useEffect, useMemo, useState } from "react";
import {
  ActionFunction,
  data,
  LoaderFunctionArgs,
  MetaFunction,
  useFetcher,
  useLoaderD<PERSON>,
  useNavigate,
} from "react-router";
import { toast } from "react-toastify";

import { ContentV2, LayoutV2 } from "~/@ui/layout/LayoutV2";
import { ConfirmModal } from "~/@ui/ConfirmModal";
import { getClients } from "~/api/notes/getClients.server";
import { getNotes } from "~/api/notes/getNotes.server";
import { configurationParameters } from "~/api/openapi/configParams";
import {
  CalendarApi,
  Configuration,
  ListNotesResponse,
  NoteApi,
  ProcessingStatus,
  TeamsApi,
} from "~/api/openapi/generated";
import SearchExperience from "~/routes/_auth.dashboard/components/SearchExperience";
import { getClientTimeZone } from "~/utils/hints";
import { combineEvents } from "~/utils/notesUtils";
import getFilteredNotes from "./utils/getFilteredNotes";
import getSearchParams from "./utils/getSearchParams";
import { logError } from "~/utils/log.server";
import { uploadNoteToCrm } from "~/api/notes/uploadToCrm.server";

export const meta: MetaFunction = () => [
  { title: "Search Results" },
  { name: "description", content: "Search across your notes" },
];

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const params = Object.fromEntries(url.searchParams.entries());
  const teamsParam = params.teams?.split(",") || [];

  const notesPromise = getNotes({
    request,
    includeTeams: teamsParam,
  }).catch(() => [] as ListNotesResponse[]);

  const config = new Configuration(await configurationParameters(request));

  const calendarEventsPromise = new CalendarApi(config)
    .calendarScheduledCalendarEvents(
      { timeZone: getClientTimeZone(request) },
      { signal: AbortSignal.timeout(5000) }
    )
    .catch(() => undefined);

  const clientPromise = getClients({ request, pageSize: 1 }).catch(() => ({
    crmSystem: undefined,
  }));

  const teamsPromise = new TeamsApi(config)
    .teamsListTeams()
    .catch(() => undefined);

  const [{ crmSystem }, notes, calendarEvents, teams] = await Promise.all([
    clientPromise,
    notesPromise,
    calendarEventsPromise,
    teamsPromise,
  ]);

  return {
    crmSystem,
    notes,
    calendarEvents,
    teams,
    params,
  };
};

export const action: ActionFunction = async ({ request }) => {
  try {
    const contentType = request.headers.get("content-type");
    if (!contentType || !contentType.includes("multipart/form-data")) {
      throw new Error("Unsupported content type");
    }

    const formData = await request.formData();
    const actionType = formData.get("actionType");
    const noteId = formData.get("noteId");

    if (typeof actionType !== "string") {
      return data(
        {
          errors: ["Invalid form data: actionType must be strings"],
        },
        { status: 400 }
      );
    }

    const configuration = new Configuration(
      await configurationParameters(request)
    );

    switch (actionType) {
      case "send-to-crm": {
        if (typeof noteId !== "string") {
          return data(
            { errors: ["Invalid form data: noteId must be a string"] },
            { status: 400 }
          );
        }
        const uploadTargetID = formData.get("uploadTargetID") as string | null;
        return await uploadNoteToCrm({
          noteId,
          request,
          uploadTargetID: uploadTargetID ?? undefined,
        });
      }
      case "delete-note": {
        if (typeof noteId !== "string") {
          return data(
            { errors: ["Invalid form data: noteId must be a string"] },
            { status: 400 }
          );
        }
        await new NoteApi(configuration).noteDeleteNote({ noteId });
        return data({ success: true });
      }

      default:
        return data(
          { errors: [`Unknown action type: ${actionType}`] },
          { status: 400 }
        );
    }
  } catch (error) {
    logError("app/routes/hub.tsx action", error);
    return data(
      { errors: ["Failed to update meeting due to server error"] },
      { status: 500 }
    );
  }
};

// NOTES (how does this component work?) -
// 1. On clicking CTA, update URL with whatever search params there are
// 2. Perform search operation on latest query params
// 3. For initial page load, populate values in the search bar from URL
const SearchPage = () => {
  const { crmSystem, notes, calendarEvents, teams, params } =
    useLoaderData<typeof loader>();

  const navigate = useNavigate();
  const fetcher = useFetcher<{ success?: boolean; error?: string }>();

  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [currentNote, setCurrentNote] = useState("");

  const [searchText, setSearchText] = useState("");
  const [selectedTeams, setSelectedTeams] = useState<string[]>([]);
  const [statuses, setStatuses] = useState<ProcessingStatus[]>([]);
  const [selectedAttendees, setSelectedAttendees] = useState<string[]>([]);

  const [isFetchingResults, setIsFetchingResults] = useState(true);

  const combinedEvents = useMemo(
    () => combineEvents(calendarEvents || [], [], [], new Set()),
    [calendarEvents]
  );

  const [loaderForTeams, setLoaderForTeams] = useState(false); // used to show loading indicator ("Working...") in Search CTA

  // For initial page load, populate values in the search bar from URL
  useEffect(() => {
    const {
      searchTextParam,
      selectedTeamsParam,
      statusesParam,
      selectedAttendeesParam,
    } = getSearchParams(params);

    setSearchText(searchTextParam);
    setSelectedTeams(selectedTeamsParam);
    setStatuses(statusesParam);
    setSelectedAttendees(selectedAttendeesParam);

    setIsFetchingResults(false);
  }, [params]);

  useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data) {
      if (fetcher.data.success) {
        toast.update("sync-to-crm", {
          render: "Note synced to CRM",
          type: toast.TYPE.SUCCESS,
          isLoading: false,
          autoClose: 2000,
        });
        toast.update("delete-note", {
          render: "Note deleted",
          type: toast.TYPE.SUCCESS,
          isLoading: false,
          autoClose: 2000,
        });
      } else {
        toast.update("sync-to-crm", {
          render: fetcher.data.error || "Failed to sync to CRM",
          type: toast.TYPE.ERROR,
          isLoading: false,
          autoClose: 2000,
        });
        toast.update("delete-note", {
          render: "Failed to delete note",
          type: toast.TYPE.SUCCESS,
          isLoading: false,
          autoClose: 2000,
        });
      }
    }
  }, [fetcher.state, fetcher.data]);

  const handleDelete = () => {
    const formData = new FormData();
    formData.append("actionType", "delete-note");
    formData.append("noteId", currentNote);
    fetcher.submit(formData, {
      method: "post",
      encType: "multipart/form-data",
    });
    toast.loading("Deleting note", {
      position: "top-center",
      autoClose: 2000,
      hideProgressBar: true,
      closeOnClick: true,
      progress: undefined,
      toastId: "delete-note",
    });
  };

  const openDeleteModal = (uuid: string) => {
    setIsDeleteModalOpen(true);
    setCurrentNote(uuid);
  };

  const closeDeleteModal = () => setIsDeleteModalOpen(false);

  const confirmDelete = () => {
    handleDelete();
    closeDeleteModal();
  };

  const onClearSearch = () => {
    setIsFetchingResults(true);
    setSearchText("");
    setSelectedTeams([]);
    setStatuses([]);
    setSelectedAttendees([]);

    navigate({ search: "" }, { replace: false });
  };

  function onSearch() {
    setIsFetchingResults(true);
    updateUrlFromSearchParams();
  }

  function updateUrlFromSearchParams() {
    const url = new URL(window.location.href);
    const params = new URLSearchParams(url.search);

    if (searchText) {
      params.set("q", searchText);
    } else {
      params.delete("q");
    }

    if (selectedTeams.length) {
      params.set("teams", selectedTeams.join(","));
    } else {
      params.delete("teams");
    }

    if (statuses.length) {
      params.set("statuses", statuses.join(","));
    } else {
      params.delete("statuses");
    }

    if (selectedAttendees.length) {
      params.set("attendees", selectedAttendees.join(","));
    } else {
      params.delete("attendees");
    }

    navigate({ search: params.toString() }, { replace: false });
  }

  const {
    searchTextParam,
    selectedTeamsParam,
    statusesParam,
    selectedAttendeesParam,
  } = getSearchParams(params);

  const { filteredCalendarEvents, filteredNotes } = !(
    searchTextParam ||
    selectedTeamsParam.length ||
    statusesParam.length ||
    selectedAttendeesParam.length
  )
    ? { filteredCalendarEvents: [], filteredNotes: [] }
    : getFilteredNotes(
        notes,
        combinedEvents,
        searchTextParam,
        statusesParam,
        selectedAttendeesParam
      );

  return (
    <LayoutV2>
      <ContentV2 className="w-80 min-w-80">
        <div className="mb-4 flex w-full flex-col items-center gap-4 sm:mb-0">
          <SearchExperience
            isSearching={
              !!(
                searchTextParam ||
                selectedTeamsParam.length ||
                statusesParam.length ||
                selectedAttendeesParam.length
              )
            }
            notes={notes}
            teams={teams}
            crmSystem={crmSystem}
            openDeleteModal={openDeleteModal}
            searchText={searchText}
            setSearchText={setSearchText}
            statuses={statuses}
            setStatuses={setStatuses}
            selectedAttendees={selectedAttendees}
            setSelectedAttendees={setSelectedAttendees}
            selectedTeams={selectedTeams}
            setSelectedTeams={setSelectedTeams}
            filteredCalendarEvents={filteredCalendarEvents}
            filteredNotes={filteredNotes}
            onClearSearch={onClearSearch}
            onSearch={onSearch}
            isFetchingResults={isFetchingResults || loaderForTeams}
          />
        </div>

        <ConfirmModal
          isOpen={isDeleteModalOpen}
          onClose={closeDeleteModal}
          onConfirm={confirmDelete}
          title="Confirm Delete"
          message="Are you sure you want to delete this note? This will delete the note for all users it's shared with."
        />
      </ContentV2>
    </LayoutV2>
  );
};

export default SearchPage;
