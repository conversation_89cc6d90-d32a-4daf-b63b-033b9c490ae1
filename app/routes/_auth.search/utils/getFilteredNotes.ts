import { ListNotesResponse, ProcessingStatus } from "~/api/openapi/generated";
import { CombinedEvent } from "~/utils/notesUtils";

export default function getFilteredNotes(
  notes: ListNotesResponse[],
  calendarEvents: CombinedEvent[],
  searchText: string,
  statuses: ProcessingStatus[],
  selectedAttendees: string[]
) {
  const alteredSearchText = searchText.toLowerCase().trim();

  const filteredCalendarEvents = calendarEvents.filter((event) => {
    const searchQueryMatch =
      !alteredSearchText ||
      event.meetingName.toLowerCase().includes(alteredSearchText) ||
      event.attendees?.some((attendee) =>
        attendee.name.toLowerCase().includes(alteredSearchText)
      );

    const attendeeMatch =
      selectedAttendees.length === 0 ||
      event.attendees?.some((attendee) =>
        selectedAttendees.includes(attendee.name)
      );

    return searchQueryMatch && attendeeMatch;
  });

  const filteredNotes = notes.filter((note) => {
    // search based on meeting name, keywords, attendees
    const searchQueryMatch =
      !alteredSearchText ||
      note.meetingName.toLowerCase().includes(alteredSearchText) ||
      note.tags.some((tag) => tag.toLowerCase().includes(alteredSearchText)) ||
      note.attendees?.some((attendee) =>
        attendee.name.toLowerCase().includes(alteredSearchText)
      );

    const statusMatch = statuses.length === 0 || statuses.includes(note.status);

    const attendeeMatch =
      selectedAttendees.length === 0 ||
      note.attendees?.some((attendee) =>
        selectedAttendees.includes(attendee.name)
      );

    return searchQueryMatch && statusMatch && attendeeMatch;
  });

  return { filteredCalendarEvents, filteredNotes };
}
