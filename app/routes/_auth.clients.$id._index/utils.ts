import { format } from "date-fns";
import { ApiRoutersClientClientResponse } from "~/api/openapi/generated";
import { capitalize } from "~/utils/strings";

export function getClientData(client: ApiRoutersClientClientResponse) {
  const {
    firstName,
    lastName,
    email,
    phoneNumber,
    jobTitle,
    dateOfBirth,
    clientType,
    onboardingDate,
  } = client;
  return [
    {
      label: "First Name",
      value: firstName,
    },
    {
      label: "Last Name",
      value: lastName,
    },
    {
      label: "Date of Birth",
      value: dateOfBirth ? format(dateOfBirth, "MMM d, yyyy") : "",
    },
    {
      label: "Job Title",
      value: jobTitle,
    },
    {
      label: "Email",
      value: email,
    },
    {
      label: "Phone Number",
      value: phoneNumber,
    },
    {
      label: "Type",
      value: capitalize(clientType),
    },
    {
      label: "Onboarding Date",
      value: onboardingDate ? format(onboardingDate, "MMM d, yyyy") : "",
    },
  ];
}
