import { useState, useCallback, useEffect } from "react";
import { useFetcher } from "react-router";
import { Typography } from "~/@ui/Typography";
import { EmailCard } from "~/@ui/clients/EmailCard";
import {
  EmailSummary,
  EmailDetail,
  EmailListResponse,
} from "~/api/openapi/generated";
import { Skeleton } from "~/@shadcn/ui/skeleton";

type Props = {
  clientEmail: string | null;
};

type ActionResponse = EmailListResponse | { success: false; error: string };

export const EmailsTab = ({ clientEmail }: Props) => {
  const [emails, setEmails] = useState<EmailSummary[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const emailsFetcher = useFetcher<ActionResponse>();
  const emailDetailFetcher = useFetcher<
    EmailDetail | { success: false; error: string }
  >();

  useEffect(() => {
    if (!clientEmail) return;

    setLoading(true);
    setError(null);

    // Fetch emails related to this client
    const formData = new FormData();
    formData.append("actionType", "get-emails");
    formData.append("sender", clientEmail);

    emailsFetcher.submit(formData, {
      method: "post",
      encType: "multipart/form-data",
    });

    // emailsFetcher doesn't need to be in the dependency array; we only need to make the API call if clientEmail changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [clientEmail]);

  useEffect(() => {
    if (emailsFetcher.state === "loading") {
      setLoading(true);
      setError(null);
    } else if (
      emailsFetcher.state === "idle" &&
      emailsFetcher.data !== undefined
    ) {
      setLoading(false);

      // Check if it's an error response
      if (emailsFetcher.data && typeof emailsFetcher.data === "object") {
        if (
          "success" in emailsFetcher.data &&
          emailsFetcher.data.success === false
        ) {
          // Error response from action handler
          const errorResponse = emailsFetcher.data as {
            success: false;
            error: string;
          };
          setError(
            `Failed to load emails: ${errorResponse.error || "Unknown error"}`
          );
        } else if ("emails" in emailsFetcher.data) {
          // Success response - EmailListResponse format
          const successResponse = emailsFetcher.data as EmailListResponse;
          setEmails(successResponse.emails);
          setError(null);
        } else {
          setError("Unexpected response format");
        }
      } else {
        setError("No data received");
      }
    }
  }, [emailsFetcher.data, emailsFetcher.state, clientEmail]);

  const handleEmailExpand = useCallback(
    (provider: string, messageId: string) => {
      const formData = new FormData();
      formData.append("actionType", "get-email-detail");
      formData.append("provider", provider);
      formData.append("messageId", messageId);

      emailDetailFetcher.submit(formData, {
        method: "post",
        encType: "multipart/form-data",
      });
    },
    [emailDetailFetcher]
  );

  if (!clientEmail) {
    return (
      <Typography className="mt-4 self-stretch text-center" color="secondary">
        No email address available for this client.
      </Typography>
    );
  }

  if (loading) {
    return (
      <div className="space-y-2">
        <Skeleton className="h-20 w-full" />
        <Skeleton className="h-20 w-full" />
        <Skeleton className="h-20 w-full" />
      </div>
    );
  }

  if (error) {
    return (
      <Typography className="mt-4 self-stretch text-center" color="secondary">
        {error}
      </Typography>
    );
  }

  const sortedEmails = emails.sort(
    (a, b) =>
      new Date(b.receivedTime).getTime() - new Date(a.receivedTime).getTime()
  );

  return (
    <>
      {sortedEmails.length > 0 ? (
        <div className="space-y-2">
          {sortedEmails.map((email) => (
            <EmailCard
              key={`${email.provider}-${email.id}`}
              email={email}
              onExpand={handleEmailExpand}
              emailDetailFetcher={emailDetailFetcher}
            />
          ))}
        </div>
      ) : (
        <Typography className="mt-4 self-stretch text-center" color="secondary">
          No emails found for this client.
        </Typography>
      )}
    </>
  );
};
