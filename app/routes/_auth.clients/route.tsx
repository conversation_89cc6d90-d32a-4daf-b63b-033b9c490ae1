import { useState, useEffect, useMemo } from "react";
import { NavLink, Outlet, useLocation, useNavigate } from "react-router";
import { toast } from "react-toastify";

import AutoSizer from "react-virtualized-auto-sizer";
import { FixedSizeList, ListChildComponentProps } from "react-window";

import { Typography } from "~/@ui/Typography";
import { ContentV2, LayoutV2 } from "~/@ui/layout/LayoutV2";
import { Fab } from "~/@ui/Fab";
import { ClientCard } from "~/@ui/clients/ClientCard";
import { useDebounce } from "~/utils/useDebounce";
import { Spinner } from "~/@ui/assets/Spinner";
import { X, Plus } from "lucide-react";
import { useFlag } from "~/context/flags";
import CreateEditClientFlow from "~/@ui/clients/CreateEditClientFlow";
import { CrmApi } from "~/api/openapi/generated";
import { useAPIConfiguration } from "~/context/apiAuth";

type ClientType = {
  uuid: string;
  name: string;
  type: string;
};

const Route = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [filteredClients, setFilteredClients] = useState<ClientType[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [cursor, setCursor] = useState<string | null>("");
  const [isLoadingSearchResults, setIsLoadingSearchResults] = useState(false); // triggered by search term changes

  const [isLoading, setIsLoading] = useState(false); // `true` if API call is in progress - for both search term change & pagination

  const isClientCreationEnabled = useFlag("EnableClientCreation");

  const debouncedSearchQuery = useDebounce(searchQuery, 400);

  // get `action` query param from URL
  const action = new URLSearchParams(location.search).get("action");

  const config = useAPIConfiguration();
  const crmAPIClient = useMemo(() => new CrmApi(config), [config]);

  // re-fetch clients when search query changes
  useEffect(() => {
    (async function () {
      setIsLoading(true);
      setIsLoadingSearchResults(true);

      const response = await crmAPIClient.crmGetClientList({
        q: debouncedSearchQuery,
        pageSize: 20,
      });

      setFilteredClients(response.clients);
      setCursor(response.nextPageToken ?? null);

      setIsLoading(false);
      setIsLoadingSearchResults(false);
    })();
  }, [debouncedSearchQuery, crmAPIClient]);

  // open create client sheet if action = create
  useEffect(() => {
    if (isClientCreationEnabled && action === "create") {
      setIsCreateModalOpen(true);
    }
  }, [isClientCreationEnabled, action]);

  const loadMore = async () => {
    if (isLoading) {
      return;
    }

    setIsLoading(true);
    const response = await crmAPIClient.crmGetClientList({
      q: debouncedSearchQuery,
      pageSize: 20,
      cursor,
    });

    setFilteredClients((prev) => [...prev, ...response.clients]);
    setCursor(response.nextPageToken || "");

    setIsLoading(false);
  };

  const handleClearSearch = () => {
    setSearchQuery("");
  };

  const Row = ({ index, style }: ListChildComponentProps) => {
    const client = filteredClients[index];
    if (!client) {
      return null;
    }
    return (
      <div style={style}>
        <div className="py-2" data-testid="client-card-wrapper">
          <ClientCard
            clientName={client.name}
            clientType={client.type}
            to={{ pathname: `/clients/${client.uuid}` }}
            className="w-full"
          />
        </div>
      </div>
    );
  };

  const closeModal = () => {
    // remove `action` query param from URL
    const params = new URLSearchParams(location.search);
    params.delete("action");
    navigate({ search: params.toString() }, { replace: true });

    // collapse sheet
    setIsCreateModalOpen(false);
  };

  const onClientCreate = (clientUuid: string) => {
    // collapse sheet
    setIsCreateModalOpen(false);

    // redirect to URL of the new client; full page reload is used to force re-render
    window.location.href = `/clients/${clientUuid}`;
  };

  return (
    <LayoutV2>
      <ContentV2
        floatingAction={
          <Fab asChild>
            <NavLink
              to={isClientCreationEnabled ? "?action=create" : "/notes/create"}
            >
              <Plus />
            </NavLink>
          </Fab>
        }
        className="relative w-80 min-w-80"
      >
        <div className="relative flex w-full flex-col gap-4 text-gray-500">
          <input
            type="text"
            placeholder="Search by client name..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="block w-full rounded border border-gray-300 p-2 shadow-sm"
            data-testid="search-input"
          />
          <div className="pointer-events-none absolute right-2 top-1/2 flex -translate-y-1/2 transform gap-1">
            {searchQuery && (
              <button
                onClick={handleClearSearch}
                className="pointer-events-auto"
                data-testid="clear-search-icon"
              >
                <X className="!h-5 !w-5" />
              </button>
            )}
            {isLoadingSearchResults && (
              <span data-testid="search-spinner-icon">
                <Spinner />
              </span>
            )}
          </div>
        </div>

        {filteredClients.length > 0 ? (
          <AutoSizer>
            {({ height, width }) => (
              <FixedSizeList
                height={height - 38} // to avoid the extra scrollbar
                width={width}
                itemSize={94}
                itemCount={filteredClients.length}
                onItemsRendered={({
                  visibleStopIndex,
                }: {
                  visibleStopIndex: number;
                }) => {
                  // load more data when user scrolls near the end
                  if (
                    visibleStopIndex >= filteredClients.length - 5 &&
                    cursor
                  ) {
                    loadMore();
                  }
                }}
              >
                {Row}
              </FixedSizeList>
            )}
          </AutoSizer>
        ) : (
          <Typography
            className="mt-4 self-stretch text-center"
            color="secondary"
          >
            Nothing here... yet.
          </Typography>
        )}

        {isCreateModalOpen && (
          <CreateEditClientFlow
            onOpenChange={closeModal}
            onSuccess={onClientCreate}
            onError={(msg: string) => {
              toast.error(msg, {
                autoClose: 2000,
              });
            }}
            successMsg="Client created. Redirecting.."
          />
        )}

        {isLoading && (
          <div className="pointer-events-none absolute bottom-0 left-4 right-4 flex h-6 items-center justify-center rounded-t-lg bg-black text-xs text-primary-foreground opacity-50">
            Loading...
          </div>
        )}
      </ContentV2>

      <Outlet context={{ clients: filteredClients }} key={location.pathname} />
    </LayoutV2>
  );
};

export default Route;
