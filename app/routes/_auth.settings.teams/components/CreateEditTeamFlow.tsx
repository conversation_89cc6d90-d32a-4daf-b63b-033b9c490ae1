import { useEffect, useMemo, useState } from "react";
import { use<PERSON><PERSON>cher, useRevalidator } from "react-router";
import { RotateCcw, Save, X } from "lucide-react";

import { Button } from "~/@shadcn/ui/button";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from "~/@shadcn/ui/sheet";
import renderFormFields, { FormFieldType } from "~/utils/renderFormFields";
import {
  ApiRoutersUserModelsUserResponse,
  TeamCreate,
  TeamDetailResponse,
  TeamsApi,
  UserApi,
} from "~/api/openapi/generated";
import { useAPIConfiguration } from "~/context/apiAuth";

import "react-phone-number-input/style.css";

type Props = {
  onOpenChange: (isOpen: boolean) => void;
  onSuccess: () => void;
  onError: (msg: string) => void;
  organizationId?: string;

  isEditing?: boolean; // this flag determines whether the create flow or edit flow is being used
  teamUuid?: string; // to be used only for edit flow
};

type TeamFormData = Pick<TeamCreate, "memberIds" | "name" | "viewerIds">;

const CreateTeamFlow = (props: Props) => {
  const {
    onOpenChange,
    onSuccess,
    onError,
    organizationId,
    isEditing,
    teamUuid,
  } = props;

  const [formData, setFormData] = useState<TeamFormData>(initializeData());
  const [users, setUsers] = useState<ApiRoutersUserModelsUserResponse[]>([]); // to be used for members & viewers
  const [isInvalid, setIsInvalid] = useState(false);

  const createFetcher = useFetcher();
  const updateFetcher = useFetcher();

  const isSaving =
    createFetcher.state !== "idle" || updateFetcher.state !== "idle";

  // status handling for fetchers
  useEffect(() => {
    if (createFetcher.state === "idle" && createFetcher.data) {
      if (createFetcher.data.success) {
        onSuccess();
      } else {
        onError("Failed to create team. Please try again");
      }
    }
  }, [createFetcher.state, createFetcher.data, onSuccess, onError]);
  useEffect(() => {
    if (updateFetcher.state === "idle" && updateFetcher.data) {
      if (updateFetcher.data.success) {
        onSuccess();
      } else {
        onError("Failed to update team. Please try again");
      }
    }
  }, [updateFetcher.state, updateFetcher.data, onSuccess, onError]);

  const apiConfig = useAPIConfiguration();
  const userApiClient = useMemo(() => new UserApi(apiConfig), [apiConfig]);
  const teamsApiClient = useMemo(() => new TeamsApi(apiConfig), [apiConfig]);

  // if any required fields are empty; disable CTA
  useEffect(() => {
    const hasEmptyRequiredFields = getFormFields([]).some(
      (field) => field.required && !formData[field.id]
    );
    setIsInvalid(hasEmptyRequiredFields);
  }, [formData]);

  // load users
  useEffect(() => {
    (async () => {
      const { users } = await userApiClient.userListUsers();
      setUsers(users);
    })();
  }, [userApiClient]);

  // load data for team if editing
  useEffect(() => {
    if (!isEditing || !teamUuid) {
      return;
    }
    (async () => {
      const teamData = await teamsApiClient.teamsGetTeam({
        teamUuid,
      });
      setFormData(initializeData(teamData));
    })();
  }, [teamsApiClient, isEditing, teamUuid]);

  const onSave = () => {
    // handle create flow
    if (!isEditing) {
      if (!organizationId) {
        onError("Organization ID is missing. Please try again.");
        return;
      }

      createFetcher.submit(
        {
          actionType: "team-create",
          organizationId,
          details: JSON.stringify(formData),
        },
        {
          method: "post",
          encType: "multipart/form-data",
        }
      );
      return;
    }

    if (!teamUuid) {
      onError("Team UUID is missing. Please try again");
      return;
    }

    updateFetcher.submit(
      {
        actionType: "team-update",
        teamUuid,
        details: JSON.stringify(formData),
      },
      {
        method: "post",
        encType: "multipart/form-data",
      }
    );
  };

  // reset form data to initial values (computed from prefilled details)
  const onReset = () => {
    setFormData(initializeData());
  };

  const title = isEditing ? "Edit Team" : "Create a New Team";

  const ctaText = isEditing
    ? isSaving
      ? "Updating…"
      : "Update Team"
    : isSaving
      ? "Creating…"
      : "Create Team";

  return (
    <Sheet open onOpenChange={onOpenChange}>
      <SheetContent className="flex w-full flex-col">
        <SheetHeader>
          <SheetTitle>{title}</SheetTitle>
          <SheetDescription />
        </SheetHeader>

        <div className="flex grow flex-col justify-start gap-4 overflow-auto px-0.5">
          {renderFormFields(getFormFields(users), formData, setFormData)}
        </div>

        <SheetFooter className="shrink-0 flex-col gap-y-2 sm:justify-start">
          <Button onClick={onSave} disabled={isInvalid || isSaving}>
            <Save />
            {ctaText}
          </Button>
          {isEditing ? (
            <Button
              onClick={() => onOpenChange(false)}
              variant="ghost"
              disabled={isSaving}
            >
              <X />
              Cancel
            </Button>
          ) : (
            <Button onClick={onReset} variant="ghost" disabled={isSaving}>
              <RotateCcw />
              Reset
            </Button>
          )}
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};

function initializeData(data?: TeamDetailResponse): TeamFormData {
  if (!data) {
    return {
      name: "",
      memberIds: [],
      viewerIds: [],
    };
  }

  const { name, members, viewers } = data;

  return {
    name,
    memberIds: members.map(({ uuid }) => uuid),
    viewerIds: viewers.map(({ uuid }) => uuid),
  };
}

function getFormFields(
  users: ApiRoutersUserModelsUserResponse[]
): FormFieldType<TeamFormData>[] {
  return [
    {
      id: "name",
      label: "Name",
      type: "text",
      required: true,
    },
    {
      id: "memberIds",
      label: "Members",
      tooltip:
        "Members' content will be shared with viewers, but not with other members.",
      type: "dropdown-multi",
      options: users.map(({ name, uuid }) => ({
        label: name || "(Unnamed user)",
        value: uuid,
      })),
    },
    {
      id: "viewerIds",
      label: "Viewers",
      tooltip:
        "Viewers will be able to view and edit all content for members of the team, but not for other viewers.",
      type: "dropdown-multi",
      options: users.map(({ name, uuid }) => ({
        label: name || "(Unnamed user)",
        value: uuid,
      })),
    },
  ];
}

export default CreateTeamFlow;
