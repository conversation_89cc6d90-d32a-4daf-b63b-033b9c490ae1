import { useState } from "react";
import {
  ActionFunctionArgs,
  Link,
  LoaderFunctionArgs,
  useLoaderData,
} from "react-router";
import { ArrowLeft, Edit } from "lucide-react";
import { toast } from "react-toastify";

import { Button } from "~/@shadcn/ui/button";
import CreateEditTeamFlow from "./components/CreateEditTeamFlow";
import { Configuration, TeamResponse, TeamsApi } from "~/api/openapi/generated";
import { getUserSessionOrRedirect } from "~/auth/authenticator.server";
import { configurationParameters } from "~/api/openapi/configParams";
import { data } from "~/utils/reactRouter";
import { useHydrated } from "~/utils/hydration";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { orgId } = await getUserSessionOrRedirect(request);

  const configuration = new Configuration(
    await configurationParameters(request)
  );

  const { teams } = await new TeamsApi(configuration).teamsListTeams();

  return { organizationId: orgId ?? undefined, teams };
};

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    const formData = await request.formData();
    const actionType = formData.get("actionType");

    const configuration = new Configuration(
      await configurationParameters(request)
    );

    switch (actionType) {
      case "team-create": {
        const details = formData.get("details");
        const parsedDetails =
          typeof details === "string" ? JSON.parse(details) : details;
        const organizationId = formData.get("organizationId");

        await new TeamsApi(configuration).teamsCreateTeam({
          teamCreate: { ...parsedDetails, organizationId },
        });

        return data({ success: true });
      }
      case "team-update": {
        const teamUuid = formData.get("teamUuid");
        const details = formData.get("details");
        const parsedDetails =
          typeof details === "string" ? JSON.parse(details) : details;

        await new TeamsApi(configuration).teamsUpdateTeam({
          teamUuid: teamUuid as string,
          teamUpdate: parsedDetails,
        });

        return data({ success: true });
      }
    }
  } catch (e) {
    // eslint-disable-next-line no-console
    console.error("Action in /teams failed", e);
    return data({ success: false });
  }
};

const Route = () => {
  const { organizationId, teams } = useLoaderData<typeof loader>();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingTeamUuid, setEditingTeamUuid] = useState(""); // UUID of team being edited

  const onCreateTeam = () => {
    // show toast
    toast.success("Team created");

    // close modal
    setIsCreateModalOpen(false);
  };

  const onEditTeam = () => {
    // show toast
    toast.success("Team updated");
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center">
        <Button className="mr-2 md:hidden" size="icon-sm" variant="outline">
          <Link to="/settings">
            <ArrowLeft />
          </Link>
        </Button>

        <h2 className="text-2xl font-semibold">Teams</h2>

        <Button onClick={() => setIsCreateModalOpen(true)} className="ml-auto">
          Create Team
        </Button>
      </div>

      {teams && teams.length > 0 ? (
        <div className="grid grid-cols-[repeat(auto-fit,minmax(350px,1fr))] gap-2">
          {teams.map((team) => (
            <TeamCard
              key={team.uuid}
              data={team}
              onClick={() => setEditingTeamUuid(team.uuid)}
            />
          ))}
        </div>
      ) : (
        <div className="text-sm text-muted-foreground">No teams found.</div>
      )}

      {isCreateModalOpen && (
        <CreateEditTeamFlow
          onOpenChange={setIsCreateModalOpen}
          onSuccess={onCreateTeam}
          onError={(msg: string) => {
            toast.error(msg, {
              autoClose: 2000,
            });
          }}
          organizationId={organizationId}
        />
      )}

      {editingTeamUuid && (
        <CreateEditTeamFlow
          onOpenChange={() => setEditingTeamUuid("")}
          onSuccess={onEditTeam}
          onError={(msg: string) => {
            toast.error(msg, {
              autoClose: 2000,
            });
          }}
          isEditing
          teamUuid={editingTeamUuid}
        />
      )}
    </div>
  );
};

const TeamCard = ({
  data,
  onClick,
}: {
  data: TeamResponse;
  onClick: () => void;
}) => {
  const { name, created } = data;
  const isHydrated = useHydrated();

  return (
    <div
      onClick={onClick}
      className="flex cursor-pointer justify-between rounded-sm border border-muted p-2 text-sm transition-colors hover:bg-muted/50"
    >
      <div>
        <div className="font-semibold">{name}</div>
        <div>Created on {isHydrated ? created.toLocaleDateString() : "…"}</div>
      </div>

      <Edit className="shrink-0 text-muted-foreground" size={16} />
    </div>
  );
};

export default Route;
