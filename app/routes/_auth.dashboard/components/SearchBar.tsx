import { useEffect, useState } from "react";
import { Info, <PERSON>Filter, LoaderCircle, SearchIcon } from "lucide-react";

import { Button } from "~/@shadcn/ui/button";
import {
  Drawer,
  DrawerContent,
  DrawerFooter,
  Drawer<PERSON>eader,
  DrawerTitle,
} from "~/@shadcn/ui/drawer";
import { Label } from "~/@shadcn/ui/label";
import { MultiSelect } from "~/@ui/MultiSelect";
import { ProcessingStatus, TeamListResponse } from "~/api/openapi/generated";
import { useUserAgent } from "~/context/userAgent";
import { cn } from "~/@shadcn/utils";
import { useFlag } from "~/context/flags";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";
import useLocalStorage, { localStorageKeys } from "~/utils/useLocalStorage";

type Props = {
  searchText: string;
  setSearchText: (searchText: string) => void;
  statuses: ProcessingStatus[];
  setStatuses: React.Dispatch<React.SetStateAction<ProcessingStatus[]>>;
  selectedAttendees: string[];
  setSelectedAttendees: React.Dispatch<React.SetStateAction<string[]>>;
  selectedTeams?: string[];
  setSelectedTeams?: React.Dispatch<React.SetStateAction<string[]>>;
  statusOptions: { label: string; value: ProcessingStatus }[];
  clientOptions: { label: string; value: string }[];
  teams?: TeamListResponse;
  onReset: () => void;
  onSearch: () => void;
  isFetchingResults: boolean;
  shouldSaveToLocalStorage?: boolean;
};
const Search = ({
  searchText,
  setSearchText,
  statuses,
  setStatuses,
  selectedAttendees,
  setSelectedAttendees,
  selectedTeams,
  setSelectedTeams,
  statusOptions,
  clientOptions,
  teams,
  onReset,
  onSearch,
  isFetchingResults,
  shouldSaveToLocalStorage,
}: Props) => {
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [localStorageValue, setLocalStorageValue] = useLocalStorage(
    localStorageKeys.ADVISOR_HUB_FILTERS,
    {}
  );
  const { isMobile } = useUserAgent();

  const isSrpEnabled = useFlag("EnableSearchPage");
  const isTeamsFilterEnabled =
    useFlag("EnableTeamsFilterForSearch") && isSrpEnabled;

  // load values from LS if shouldSaveToLocalStorage is true
  useEffect(() => {
    if (!shouldSaveToLocalStorage || !isTeamsFilterEnabled) {
      return;
    }

    setStatuses(localStorageValue.statuses || []);
    setSelectedAttendees(localStorageValue.attendees || []);
    setSelectedTeams && setSelectedTeams(localStorageValue.teams || []);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isTeamsFilterEnabled]);

  const updateValues = (key: string, value: string[]) => {
    // save data to LS
    shouldSaveToLocalStorage &&
      isTeamsFilterEnabled &&
      setLocalStorageValue({
        ...localStorageValue,
        [key]: value,
      });

    // update local state
    switch (key) {
      case "statuses":
        setStatuses(value as ProcessingStatus[]);
        break;
      case "attendees":
        setSelectedAttendees(value);
        break;
      case "teams":
        setSelectedTeams && setSelectedTeams(value);
        break;
    }
  };

  if (isMobile) {
    let filterCount = 0;
    selectedTeams?.length && filterCount++;
    statuses.length && filterCount++;
    selectedAttendees.length && filterCount++;

    return (
      <div className="my-4 flex w-full max-w-[1100px] items-center gap-2 rounded-lg border shadow-md">
        {/* input bar */}
        <input
          type="text"
          placeholder="Type name or keywords…"
          className="grow rounded-lg p-3 text-sm outline-none"
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          autoFocus
        />

        {/* filter icon */}
        <button
          onClick={() => setIsFilterModalOpen(true)}
          className="relative inline-flex size-9 items-center justify-center"
        >
          <ListFilter size={18} />
          {!!filterCount && (
            <span className="absolute -right-1 -top-1 inline-flex size-5 items-center justify-center rounded-full text-xs font-semibold">
              {filterCount}
            </span>
          )}
        </button>

        {/* search CTA */}
        <button
          className="inline-flex h-full items-center rounded-r-lg bg-primary px-5 text-primary-foreground"
          onClick={onSearch}
        >
          {isFetchingResults ? (
            <LoaderCircle size={18} className="animate-spin" />
          ) : (
            <SearchIcon size={18} />
          )}
        </button>

        {/* filters modal */}
        <Drawer open={isFilterModalOpen} onOpenChange={setIsFilterModalOpen}>
          <DrawerContent>
            <DrawerHeader>
              <DrawerTitle>Search filters</DrawerTitle>
            </DrawerHeader>

            <div className="flex flex-col gap-4 p-4">
              {isTeamsFilterEnabled &&
                !!teams?.teams?.length &&
                selectedTeams && (
                  <div className="flex flex-col gap-2">
                    <Label>Team view</Label>
                    <MultiSelect
                      options={teams.teams.map(({ name, uuid }) => ({
                        label: name,
                        value: uuid,
                      }))}
                      selected={selectedTeams}
                      onChange={(value) => updateValues("teams", value)}
                      placeholder="Select Teams"
                      triggerClassName="shadow-none w-fit"
                    />
                  </div>
                )}

              <div className="flex flex-col gap-2">
                <Label>Status</Label>
                <MultiSelect
                  options={statusOptions}
                  selected={statuses}
                  onChange={(value) => updateValues("statuses", value)}
                  placeholder="Select Status"
                  triggerClassName="shadow-none w-fit"
                  useLabelForSearch
                  modal
                />
              </div>

              <div className="flex flex-col gap-2">
                <Label>Attendees</Label>
                <MultiSelect
                  options={clientOptions}
                  selected={selectedAttendees}
                  onChange={(value) => updateValues("attendees", value)}
                  placeholder="Select Attendees"
                  triggerClassName="shadow-none w-fit"
                  modal
                />
              </div>
            </div>

            <DrawerFooter className="flex flex-row justify-end gap-2">
              <Button variant="ghost" onClick={onReset}>
                Clear filters
              </Button>
              <Button
                variant="outline_primary"
                onClick={() => setIsFilterModalOpen(false)}
              >
                Done
              </Button>
            </DrawerFooter>
          </DrawerContent>
        </Drawer>
      </div>
    );
  }

  const shouldDisableSearch =
    (!searchText &&
      !selectedTeams?.length &&
      !statuses.length &&
      !selectedAttendees.length) ||
    isFetchingResults;

  // show the latest search UI if `EnableTeamsFilterForSearch` flag is enabled in desktop devices
  if (isTeamsFilterEnabled) {
    const triggerClassName =
      "border-none w-fit bg-transparent shadow-none hover:bg-transparent";
    const labelClassName =
      "px-2 -mb-1 text-xs text-muted-foreground uppercase font-semibold flex items-center gap-2";

    return (
      <div className="my-4 w-full max-w-[1100px] rounded-lg px-4">
        {/* input bar */}
        <div className="flex items-center rounded-full border bg-card shadow-md focus-within:border-primary">
          <input
            type="text"
            placeholder="Type name or keywords to search…"
            className="grow rounded-full p-4 outline-none"
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                !shouldDisableSearch && onSearch();
              }
            }}
            autoFocus
          />

          <button
            className={cn(
              "mr-2 inline-flex h-10 items-center gap-2 rounded-full bg-primary px-5 text-primary-foreground",
              shouldDisableSearch &&
                "pointer-events-none bg-muted text-muted-foreground"
            )}
            onClick={onSearch}
            disabled={shouldDisableSearch}
          >
            {isFetchingResults ? (
              <span className="italic">Working…</span>
            ) : (
              <>
                <SearchIcon size={18} />
                Search
              </>
            )}
          </button>
        </div>

        {/* filters and CTA */}
        <div className="mt-4 flex justify-between gap-10 px-2">
          {/* filters */}
          <div className="flex flex-wrap items-center gap-4">
            {!!teams?.teams?.length && selectedTeams && (
              <div>
                <h3 className={labelClassName}>Team view</h3>
                <MultiSelect
                  options={teams.teams.map(({ name, uuid }) => ({
                    label: name,
                    value: uuid,
                  }))}
                  selected={selectedTeams}
                  onChange={(value) => updateValues("teams", value)}
                  placeholder="Select Teams"
                  triggerClassName={triggerClassName}
                />
              </div>
            )}

            <div>
              <h3 className={labelClassName}>Status</h3>
              <MultiSelect
                options={statusOptions}
                selected={statuses}
                onChange={(value) => updateValues("statuses", value)}
                placeholder="Select Status"
                triggerClassName={triggerClassName}
                useLabelForSearch
              />
            </div>

            <div>
              <h3 className={labelClassName}>Attendees</h3>
              <MultiSelect
                options={clientOptions}
                selected={selectedAttendees}
                onChange={(value) => updateValues("attendees", value)}
                placeholder="Select Attendees"
                triggerClassName={triggerClassName}
              />
            </div>
          </div>
        </div>
      </div>
    );
  }

  // old search UI - for desktop devices with `EnableTeamsFilterForSearch` flag disabled
  return (
    <div className="my-4 flex w-full max-w-[1100px] items-center gap-2 rounded-lg border shadow-lg focus-within:border-primary">
      <input
        type="text"
        placeholder="Type name or keywords to search…"
        className="grow rounded-lg p-4 outline-none"
        value={searchText}
        onChange={(e) => setSearchText(e.target.value)}
        onKeyDown={(e) => {
          if (e.key === "Enter") {
            !shouldDisableSearch && onSearch();
          }
        }}
        autoFocus
      />

      <MultiSelect
        options={statusOptions}
        selected={statuses}
        onChange={(statuses) => setStatuses(statuses as ProcessingStatus[])}
        placeholder="Select Status"
        triggerClassName="shadow-none border-none w-fit"
        useLabelForSearch
      />

      <MultiSelect
        options={clientOptions}
        selected={selectedAttendees}
        onChange={setSelectedAttendees}
        placeholder="Select Attendees"
        triggerClassName="shadow-none border-none w-fit"
      />

      <button
        className={cn(
          "inline-flex h-full items-center gap-2 rounded-r-lg bg-primary px-5 text-primary-foreground",
          shouldDisableSearch &&
            "pointer-events-none bg-muted text-muted-foreground"
        )}
        onClick={onSearch}
        disabled={shouldDisableSearch}
      >
        {isFetchingResults ? (
          <LoaderCircle size={18} className="animate-spin" />
        ) : (
          <SearchIcon size={18} />
        )}
      </button>
    </div>
  );
};

export default Search;
