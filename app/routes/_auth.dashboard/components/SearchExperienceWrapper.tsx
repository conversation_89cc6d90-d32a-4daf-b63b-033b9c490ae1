import { useState } from "react";
import { useNavigate } from "react-router";

import SearchExperience from "./SearchExperience";
import {
  ListNotesResponse,
  ProcessingStatus,
  TeamListResponse,
} from "~/api/openapi/generated";

const SearchExperienceWrapper = ({
  notes,
  teams,
  selectedTeams,
  setSelectedTeams,
  statuses,
  setStatuses,
  selectedAttendees,
  setSelectedAttendees,
}: {
  notes: ListNotesResponse[];
  teams?: TeamListResponse;
  selectedTeams?: string[];
  setSelectedTeams?: React.Dispatch<React.SetStateAction<string[]>>;
  statuses: ProcessingStatus[];
  setStatuses: React.Dispatch<React.SetStateAction<ProcessingStatus[]>>;
  selectedAttendees: string[];
  setSelectedAttendees: React.Dispatch<React.SetStateAction<string[]>>;
}) => {
  const [searchText, setSearchText] = useState("");
  const [isFetchingResults, setIsFetchingResults] = useState(false);

  const navigate = useNavigate();

  // construct search page URL with query params, and redirect
  const onSearch = () => {
    setIsFetchingResults(true);

    const url = new URL(window.location.href);
    const params = new URLSearchParams(url.search);

    if (searchText) {
      params.set("q", searchText);
    } else {
      params.delete("q");
    }

    if (selectedTeams?.length) {
      params.set("teams", selectedTeams.join(","));
    } else {
      params.delete("teams");
    }

    if (statuses.length) {
      params.set("statuses", statuses.join(","));
    } else {
      params.delete("statuses");
    }

    if (selectedAttendees.length) {
      params.set("attendees", selectedAttendees.join(","));
    } else {
      params.delete("attendees");
    }

    navigate({
      pathname: "/search",
      search: params.toString(),
    });
  };

  return (
    <SearchExperience
      notes={notes}
      teams={teams}
      crmSystem={null}
      openDeleteModal={() => {}}
      searchText={searchText}
      setSearchText={setSearchText}
      statuses={statuses}
      setStatuses={setStatuses}
      selectedAttendees={selectedAttendees}
      setSelectedAttendees={setSelectedAttendees}
      selectedTeams={selectedTeams}
      setSelectedTeams={setSelectedTeams}
      filteredCalendarEvents={[]}
      filteredNotes={[]}
      onClearSearch={() => {}}
      onSearch={onSearch}
      isFetchingResults={isFetchingResults}
      shouldSaveToLocalStorage
    />
  );
};

export default SearchExperienceWrapper;
