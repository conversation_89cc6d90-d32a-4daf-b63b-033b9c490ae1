import SearchBar from "./SearchBar";
import SearchResults from "./SearchResults";
import {
  ListNotesResponse,
  ProcessingStatus,
  ScheduledEvent,
  TeamListResponse,
} from "~/api/openapi/generated";
import { CombinedEvent, combineEvents } from "~/utils/notesUtils";
import { useFlag } from "~/context/flags";

type Props = {
  isShowingSearchResults?: boolean;
  setIsShowingSearchResults?: (isShowingSearchResults: boolean) => void;

  isSearching?: boolean;

  notes: ListNotesResponse[];
  teams?: TeamListResponse;
  crmSystem: string | null | undefined;
  openDeleteModal: (noteId: string) => void;

  searchText: string;
  setSearchText: (searchText: string) => void;
  statuses: ProcessingStatus[];
  setStatuses: React.Dispatch<React.SetStateAction<ProcessingStatus[]>>;
  selectedAttendees: string[];
  setSelectedAttendees: React.Dispatch<React.SetStateAction<string[]>>;
  selectedTeams?: string[];
  setSelectedTeams?: React.Dispatch<React.SetStateAction<string[]>>;

  filteredCalendarEvents: CombinedEvent[];
  filteredNotes: ListNotesResponse[];

  onClearSearch: () => void;
  onSearch: () => void;
  isFetchingResults?: boolean;
  shouldSaveToLocalStorage?: boolean;
};

const SearchExperience = ({
  isShowingSearchResults,
  setIsShowingSearchResults,

  isSearching,

  notes,
  teams,
  crmSystem,
  openDeleteModal,

  searchText,
  setSearchText,
  statuses,
  setStatuses,
  selectedAttendees,
  setSelectedAttendees,
  selectedTeams,
  setSelectedTeams,

  filteredCalendarEvents,
  filteredNotes,

  onClearSearch,
  onSearch,
  isFetchingResults = false,
  shouldSaveToLocalStorage,
}: Props) => {
  const isSrpEnabled = useFlag("EnableSearchPage");

  const clientOptions = Array.from(
    new Set(notes.map((note) => note.client?.name).filter(Boolean))
  ).map((clientName) => ({
    label: clientName!,
    value: clientName!,
  }));

  const onResetMobileFilters = () => {
    setStatuses([]);
    setSelectedAttendees([]);
  };

  return (
    <>
      <SearchBar
        searchText={searchText}
        statuses={statuses}
        setStatuses={setStatuses}
        setSearchText={setSearchText}
        selectedAttendees={selectedAttendees}
        setSelectedAttendees={setSelectedAttendees}
        statusOptions={statusOptions}
        clientOptions={clientOptions}
        teams={teams}
        selectedTeams={selectedTeams}
        setSelectedTeams={setSelectedTeams}
        onReset={onResetMobileFilters}
        onSearch={onSearch}
        // `isFetchingResults` logic is only valid for scenarios with search page enabled
        isFetchingResults={isSrpEnabled ? isFetchingResults : false}
        shouldSaveToLocalStorage={shouldSaveToLocalStorage}
      />

      {(isSrpEnabled || isShowingSearchResults) && (
        <SearchResults
          isSearching={isSearching || isShowingSearchResults}
          filteredCalendarEvents={filteredCalendarEvents}
          filteredNotes={filteredNotes}
          onReset={onClearSearch}
          crmSystem={crmSystem}
          openDeleteModal={openDeleteModal}
          isFetchingResults={isFetchingResults}
        />
      )}
    </>
  );
};

const statusOptions = [
  {
    label: "Scheduled",
    value: ProcessingStatus.Scheduled,
  },
  {
    label: "Processing",
    value: ProcessingStatus.Uploaded,
  },
  {
    label: "Ready for review",
    value: ProcessingStatus.Processed,
  },
  {
    label: "Synced with CRM",
    value: ProcessingStatus.Finalized,
  },
];

export default SearchExperience;
