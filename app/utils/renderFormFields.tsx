import { TooltipPortal } from "@radix-ui/react-tooltip";
import { CircleHelp } from "lucide-react";
import PhoneInputWithCountrySelect from "react-phone-number-input";

import { FormField, FormLabel } from "~/@shadcn/ui/form";
import { Input } from "~/@shadcn/ui/input";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/@shadcn/ui/select";
import { Switch } from "~/@shadcn/ui/switch";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";
import { MultiSelect } from "~/@ui/MultiSelect";

export default function renderFormFields<T>(
  formFields: FormFieldType<T>[],
  formData: T,
  setFormData: (data: T) => void
) {
  return formFields.map(
    ({ id, label, tooltip, type, options, required, disabled }) => {
      let inputComponent = null;

      switch (type) {
        case "text":
          inputComponent = (
            <Input
              value={formData[id] as string}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  [id]: e.target.value,
                })
              }
              disabled={disabled}
            />
          );
          break;
        case "phone":
          inputComponent = (
            <PhoneInputWithCountrySelect
              defaultCountry="US"
              placeholder="Enter phone number..."
              className="flex w-full cursor-text items-center gap-1 rounded-2xl border border-border bg-background p-3 shadow transition-colors placeholder:text-secondary hover:border-foreground hover:bg-accent has-[input:read-only]:pointer-events-none has-[button:hover]:bg-background has-[input:focus]:outline-none has-[input:focus]:ring-2 has-[input:focus]:ring-ring"
              numberInputProps={{
                className:
                  "w-full flex bg-transparent shadow-none hover:bg-transparent has-[input:focus]:ring-0 transition-colors placeholder:text-secondary focus-visible:outline-none",
              }}
              value={formData[id] as string}
              onChange={(value) =>
                setFormData({
                  ...formData,
                  [id]: value || "",
                })
              }
              disabled={disabled}
            />
          );
          break;
        case "dropdown":
          inputComponent = (
            <Select
              onValueChange={(value) =>
                setFormData({
                  ...formData,
                  [id]: value,
                })
              }
              value={formData[id] as string}
              disabled={disabled}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select an option" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  {options?.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectGroup>
              </SelectContent>
            </Select>
          );
          break;
        case "dropdown-multi":
          inputComponent = (
            <MultiSelect
              options={options || []}
              selected={(formData[id] ?? []) as string[]}
              onChange={(values) =>
                Array.isArray(values) &&
                setFormData({
                  ...formData,
                  [id]: values,
                })
              }
              placeholder="Select an option"
              triggerClassName="rounded-2xl p-3 border-border"
              useLabelForSearch
            />
          );
          break;
        case "date":
          inputComponent = (
            <Input
              type="date"
              value={formData[id] as string}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  [id]: e.target.value,
                })
              }
              disabled={disabled}
            />
          );
          break;
        case "boolean":
          inputComponent = (
            <Switch
              checked={formData[id] === true}
              onCheckedChange={(checked) =>
                setFormData({
                  ...formData,
                  [id]: checked,
                })
              }
              disabled={disabled}
            />
          );
          break;
      }
      const stringifiedId = String(id);
      return (
        <FormField
          key={stringifiedId}
          id={stringifiedId}
          name={stringifiedId}
          required={required}
        >
          <FormLabel className="mb-1 flex items-center gap-1">
            {label}
            {tooltip && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <CircleHelp className="h-5 w-5 cursor-help" />
                </TooltipTrigger>
                <TooltipPortal>
                  <TooltipContent className="max-w-[300px] whitespace-normal text-sm">
                    {tooltip}
                  </TooltipContent>
                </TooltipPortal>
              </Tooltip>
            )}
          </FormLabel>
          {inputComponent}
        </FormField>
      );
    }
  );
}

export type FormFieldType<T> = {
  id: keyof T;
  label: string;
  type: string;
  tooltip?: string;
  options?: { label: string; value: string }[];
  required?: boolean;
  disabled?: boolean;
};
