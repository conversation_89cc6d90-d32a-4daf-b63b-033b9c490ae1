import { useState, useEffect } from "react";
import { formatRelative } from "date-fns";
import { TimeStamp } from "~/@ui/TimeStamp";
import { AfterHydration } from "~/utils/hydration";
import { cn } from "~/@shadcn/utils";
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
  CardContent,
} from "~/@shadcn/ui/card";
import {
  ChevronDown,
  ChevronRight,
  Mail,
  MailOpen,
  Paperclip,
} from "lucide-react";
import { EmailSummary, EmailDetail } from "~/api/openapi/generated";
import { Skeleton } from "~/@shadcn/ui/skeleton";
import { Badge } from "~/@shadcn/ui/badge";
import { FetcherWithComponents } from "react-router";

const getFormattedTimestamp = (date: Date) => {
  const received = formatRelative(date, new Date());
  if (received.includes("at")) return `Received ${received}`;
  return `Received on ${received}`;
};

type Props = {
  email: EmailSummary;
  onExpand: (provider: string, messageId: string) => void;
  emailDetailFetcher: FetcherWithComponents<
    EmailDetail | { success: false; error: string }
  >;
  compact?: boolean;
};

export const EmailCard = ({
  email,
  onExpand,
  emailDetailFetcher,
  compact = false,
}: Props) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [emailDetail, setEmailDetail] = useState<EmailDetail | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [currentEmailId, setCurrentEmailId] = useState<string | null>(null);

  // Handle fetcher state changes
  useEffect(() => {
    // Only process if this card initiated the fetch
    if (currentEmailId !== email.id) return;

    if (emailDetailFetcher.state === "loading") {
      setIsLoading(true);
    } else if (
      emailDetailFetcher.state === "idle" &&
      emailDetailFetcher.data !== undefined
    ) {
      setIsLoading(false);

      // Check if it's an error response
      if (
        emailDetailFetcher.data &&
        "success" in emailDetailFetcher.data &&
        emailDetailFetcher.data.success === false
      ) {
        // Error - don't set emailDetail
        setCurrentEmailId(null);
      } else {
        // Success - set the email detail
        setEmailDetail(emailDetailFetcher.data as EmailDetail);
        setIsExpanded(true);
        setCurrentEmailId(null);
      }
    }
  }, [
    emailDetailFetcher.state,
    emailDetailFetcher.data,
    currentEmailId,
    email.id,
  ]);

  const handleExpand = () => {
    if (isExpanded) {
      setIsExpanded(false);
      return;
    }

    if (emailDetail) {
      setIsExpanded(true);
      return;
    }

    setCurrentEmailId(email.id);
    onExpand(email.provider, email.id);
  };

  const Icon = email.isRead ? MailOpen : Mail;

  return (
    <Card
      compact={compact}
      className={cn(
        "cursor-pointer self-stretch hover:border-foreground hover:bg-accent",
        !email.isRead && "border-l-4 border-l-blue-500"
      )}
      onClick={handleExpand}
    >
      <CardHeader className="pb-2">
        <div className="flex items-start justify-between gap-2">
          <div className="flex min-w-0 flex-1 items-start gap-2">
            <Icon
              className={cn(
                "mt-1 !h-4 !w-4 shrink-0",
                email.isRead ? "text-gray-500" : "text-blue-600"
              )}
            />
            <div className="min-w-0 flex-1">
              <CardTitle
                className="overflow-hidden text-ellipsis whitespace-nowrap text-sm font-medium"
                asChild
              >
                <div>{email.subject || "(No Subject)"}</div>
              </CardTitle>
              <CardDescription className="text-xs" asChild>
                <div>
                  <div className="truncate">From: {email.sender}</div>
                  {email.recipients && email.recipients.length > 0 && (
                    <div className="truncate">
                      To: {email.recipients.slice(0, 2).join(", ")}
                      {email.recipients.length > 2 &&
                        ` +${email.recipients.length - 2} more`}
                    </div>
                  )}
                </div>
              </CardDescription>
            </div>
          </div>

          <div className="flex shrink-0 items-center gap-1">
            {email.hasAttachments && (
              <Paperclip className="h-3 w-3 text-gray-500" />
            )}
            <div className="text-xs text-gray-500">
              <TimeStamp>
                <AfterHydration>
                  {getFormattedTimestamp(email.receivedTime)}
                </AfterHydration>
              </TimeStamp>
            </div>
            {isExpanded ? (
              <ChevronDown className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronRight className="h-4 w-4 text-gray-500" />
            )}
          </div>
        </div>

        {/* Email snippet */}
        <div className="line-clamp-2 text-xs text-gray-600">
          {email.snippet}
        </div>

        {/* Labels */}
        {email.labels && email.labels.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {email.labels.slice(0, 3).map((label) => (
              <Badge key={label} variant="secondary" className="text-xs">
                {label}
              </Badge>
            ))}
            {email.labels.length > 3 && (
              <Badge variant="secondary" className="text-xs">
                +{email.labels.length - 3} more
              </Badge>
            )}
          </div>
        )}
      </CardHeader>

      {/* Expanded content */}
      {isExpanded && (
        <CardContent className="pt-0">
          {isLoading ? (
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-20 w-full" />
            </div>
          ) : emailDetail ? (
            <div className="space-y-3 border-t pt-3">
              {/* Full recipient details */}
              <div className="space-y-1 text-xs">
                <div>
                  <strong>From:</strong> {emailDetail.sender}
                </div>
                {emailDetail.recipients &&
                  emailDetail.recipients.length > 0 && (
                    <div>
                      <strong>To:</strong> {emailDetail.recipients.join(", ")}
                    </div>
                  )}
                {emailDetail.ccRecipients &&
                  emailDetail.ccRecipients.length > 0 && (
                    <div>
                      <strong>CC:</strong> {emailDetail.ccRecipients.join(", ")}
                    </div>
                  )}
                {emailDetail.bccRecipients &&
                  emailDetail.bccRecipients.length > 0 && (
                    <div>
                      <strong>BCC:</strong>{" "}
                      {emailDetail.bccRecipients.join(", ")}
                    </div>
                  )}
                {emailDetail.sentTime && (
                  <div>
                    <strong>Sent:</strong>{" "}
                    {emailDetail.sentTime.toLocaleString()}
                  </div>
                )}
              </div>

              {/* Attachments */}
              {emailDetail.attachmentNames &&
                emailDetail.attachmentNames.length > 0 && (
                  <div className="space-y-1">
                    <div className="text-xs font-medium">Attachments:</div>
                    <div className="flex flex-wrap gap-1">
                      {emailDetail.attachmentNames.map((name, index) => (
                        <Badge
                          key={index}
                          variant="outline"
                          className="text-xs"
                        >
                          <Paperclip className="mr-1 h-3 w-3" />
                          {name}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

              <div className="max-h-96 overflow-y-auto rounded border bg-gray-50 p-3">
                {emailDetail.bodyText ? (
                  <pre className="whitespace-pre-wrap font-mono text-xs">
                    {emailDetail.bodyText}
                  </pre>
                ) : (
                  <div className="text-xs italic text-gray-500">
                    No content available
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="text-xs text-red-500">
              Failed to load email details
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
};
