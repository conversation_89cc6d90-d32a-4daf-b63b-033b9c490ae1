import { useEffect, useMemo, useState } from "react";
import { RotateCcw, Save, X } from "lucide-react";
import { datadogLogs } from "@datadog/browser-logs";

import { Button } from "~/@shadcn/ui/button";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  Sheet<PERSON>eader,
  SheetTitle,
} from "~/@shadcn/ui/sheet";
import { FormField } from "~/@shadcn/ui/form";
import {
  ApiRoutersClientClientResponse,
  ClientApi,
  ClientType,
  CreateClientRequest,
  ResponseError,
} from "~/api/openapi/generated";
import { capitalize } from "~/utils/strings";
import { useAPIConfiguration } from "~/context/apiAuth";
import renderFormFields from "~/utils/renderFormFields";
import { cn } from "~/@shadcn/utils";

import "react-phone-number-input/style.css";

type PrefilledDetails = {
  name?: string;
  email?: string;
  text?: string;
};

type Props = {
  onOpenChange: (isOpen: boolean) => void;
  onSuccess?: (clientUuid: string, formData: Record<string, any>) => void;
  onError?: (msg: string) => void;
  prefilledDetails?: PrefilledDetails;
  successMsg?: string;

  isEditing?: boolean;
  clientUuid?: string;
};

const CreateEditClientFlow = (props: Props) => {
  const {
    onOpenChange,
    onSuccess,
    onError,
    prefilledDetails,
    successMsg,
    isEditing = false,
    clientUuid,
  } = props;

  const [formData, setFormData] = useState<
    Record<keyof CreateClientRequest, string>
  >(initializeDataForCreateFlow(prefilledDetails));
  const [isInvalid, setIsInvalid] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [isFetchingDetails, setIsFetchingDetails] = useState(isEditing);

  const apiConfig = useAPIConfiguration();
  const clientAPIClient = useMemo(() => new ClientApi(apiConfig), [apiConfig]);

  // fetch data from API for edit flow
  useEffect(() => {
    if (!isEditing || !clientUuid) {
      return;
    }

    (async () => {
      const clientData = await clientAPIClient.clientGetClient({
        clientId: clientUuid,
      });

      setFormData(initializeDataForEditFlow(clientData));
      setIsFetchingDetails(false);
    })();
  }, [isEditing, clientUuid, clientAPIClient]);

  // if any required fields are empty; disable CTA
  useEffect(() => {
    const hasEmptyRequiredFields = getFormFields(isEditing).some(
      (field) => field.required && !formData[field.id]
    );
    setIsInvalid(hasEmptyRequiredFields);
  }, [formData, isEditing]);

  const onSave = async () => {
    setIsSaving(true);

    const requestBody = {
      ...formData,
      dateOfBirth: formData.dateOfBirth ? new Date(formData.dateOfBirth) : null,
      onboardingDate: formData.onboardingDate
        ? new Date(formData.onboardingDate)
        : undefined,
      phoneNumber: formData.phoneNumber || undefined,
      type: formData.type ? (formData.type as ClientType) : undefined,
    };

    if (isEditing) {
      if (!clientUuid) {
        onError?.("Client UUID is missing. Please try again.");
        setIsSaving(false);
        return;
      }

      try {
        await clientAPIClient.clientEditClient({
          clientUuid,
          clientUpdate: requestBody,
        });

        setIsSaved(true);
        onSuccess?.(clientUuid, formData);
      } catch (e) {
        datadogLogs.logger.error("Failed to update client", { error: e });
        onError?.("Failed to update client. Please try again.");
      } finally {
        setIsSaving(false);
      }

      return;
    }

    try {
      const { clientUuid } = await clientAPIClient.clientCreateClient({
        createClientRequest: requestBody,
      });

      setIsSaved(true);
      onSuccess?.(clientUuid, formData);
    } catch (e) {
      datadogLogs.logger.error("Failed to create client", { error: e });
      if (onError) {
        if (e instanceof ResponseError) {
          onError(
            e.response.status === 400
              ? "Cannot create client: a client with the same email already exists"
              : "An error occurred. Failed to create client"
          );
        } else if (typeof e === "object" && e !== null && "message" in e) {
          onError(e.message as string);
        }
      }
    } finally {
      setIsSaving(false);
    }
  };

  // reset form data to initial values (computed from prefilled details)
  const onReset = () => {
    setFormData(initializeDataForCreateFlow(prefilledDetails));
  };

  const ctaText = isEditing
    ? isSaving
      ? "Updating…"
      : "Update Client"
    : isSaving
      ? "Creating…"
      : "Create Client";

  return (
    <Sheet open onOpenChange={onOpenChange}>
      <SheetContent className="flex w-full flex-col">
        <SheetHeader>
          <SheetTitle>
            {isEditing ? "Edit Client" : "Create a New Client"}
          </SheetTitle>
          <SheetDescription />
        </SheetHeader>

        <div
          className={cn(
            "relative flex grow flex-col justify-start gap-4 px-0.5",
            isFetchingDetails ? "overflow-hidden" : "overflow-auto"
          )}
        >
          {/* loader */}
          {isFetchingDetails && (
            <div className="absolute inset-0 z-10 flex items-center justify-center bg-card/75 backdrop-blur-sm">
              Fetching client details…
            </div>
          )}

          {renderFormFields(getFormFields(isEditing), formData, setFormData)}
        </div>

        <SheetFooter className="shrink-0 flex-col gap-y-2 sm:justify-start">
          {isSaved && successMsg && (
            <div className="text-sm text-success">{successMsg}</div>
          )}

          {!(isSaved && successMsg) && (
            <>
              <Button onClick={onSave} disabled={isInvalid || isSaving}>
                <Save />
                {ctaText}
              </Button>

              {isEditing ? (
                <Button
                  onClick={() => onOpenChange(false)}
                  variant="ghost"
                  disabled={isSaving}
                >
                  <X />
                  Cancel
                </Button>
              ) : (
                <Button onClick={onReset} variant="ghost" disabled={isSaving}>
                  <RotateCcw />
                  Reset
                </Button>
              )}
            </>
          )}
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};

function initializeDataForCreateFlow(prefilledDetails?: PrefilledDetails) {
  const { name, email, text } = prefilledDetails || {};

  const data = {
    ...getFormFields(false).reduce(
      (acc, { id, defaultValue }) => {
        acc[id] = defaultValue || "";
        return acc;
      },
      {} as Record<keyof CreateClientRequest, string>
    ),
    name: name || "",
    email: email || "",
  };

  // extract name / email from `text`
  if (text) {
    if (text.includes("@")) {
      data.email = text;
    } else {
      data.name = text;
    }
  }

  return data;
}

function initializeDataForEditFlow(clientData: ApiRoutersClientClientResponse) {
  return {
    ...getFormFields(true).reduce(
      (acc, { id, type, disabled, defaultValue }) => {
        // handle by type
        switch (type) {
          // handle dates
          case "date":
            acc[id] = clientData[id]
              ? new Date(clientData[id]).toISOString().substring(0, 10)
              : "";
            break;
          default:
            const value = clientData[id];
            acc[id] =
              value instanceof Date
                ? value.toISOString().substring(0, 10)
                : value || "";
            break;
        }

        // handle disabled fields
        if (disabled) {
          const value = clientData[id];
          acc[id] =
            value instanceof Date
              ? value.toISOString().substring(0, 10)
              : value || defaultValue || "";
        }

        return acc;
      },
      {} as Record<keyof CreateClientRequest, string>
    ),
  };
}

type FormField<T> = {
  id: keyof T;
  label: string;
  type: "text" | "phone" | "dropdown" | "date";
  required?: boolean;
  options?: { label: string; value: string }[];
  defaultValue?: string;
  disabled?: boolean;
};

function getFormFields(
  isEditing: boolean
): FormField<CreateClientRequest | ApiRoutersClientClientResponse>[] {
  return [
    {
      id: "name",
      label: "Preferred Name",
      type: "text",
    },
    {
      id: "firstName",
      label: "First Name",
      type: "text",
    },
    {
      id: "lastName",
      label: "Last Name",
      type: "text",
    },
    {
      id: "dateOfBirth",
      label: "Date of Birth",
      type: "date",
    },
    {
      id: "jobTitle",
      label: "Job Title",
      type: "text",
    },
    {
      id: "email",
      label: "Email",
      type: "text",
      required: true,
    },
    {
      id: "phoneNumber",
      label: "Phone",
      type: "phone",
    },
    {
      // @ts-ignore (`type` is for CreateClientRequest, `clientType` is for ApiRoutersClientClientResponse)
      id: isEditing ? "clientType" : "type",
      label: "Client Type",
      type: "dropdown",
      options: [
        {
          label: capitalize(ClientType.Individual),
          value: ClientType.Individual,
        },
        {
          label: capitalize(ClientType.Household),
          value: ClientType.Household,
        },
      ],
      defaultValue: "individual",
      disabled: true,
    },
    {
      id: "onboardingDate",
      label: "Onboarding Date",
      type: "date",
    },
  ];
}

export default CreateEditClientFlow;
