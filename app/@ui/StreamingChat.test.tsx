import { render, screen, waitFor, act } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import StreamingChat from "./StreamingChat";
import { HttpAgent } from "@ag-ui/client";
import type { Message } from "@ag-ui/client";
import { TooltipProvider } from "~/@shadcn/ui/tooltip";

vi.mock("@ag-ui/client", () => ({
  HttpAgent: vi.fn(),
}));

vi.mock("~/context/apiAuth", () => ({
  useAPIConfiguration: vi.fn(),
}));

vi.mock("@datadog/browser-logs", () => ({
  datadogLogs: {
    logger: {
      error: vi.fn(),
    },
  },
}));

import { useAPIConfiguration } from "~/context/apiAuth";
import { datadogLogs } from "@datadog/browser-logs";

const renderWithTooltipProvider = (component: React.ReactElement) => {
  return render(<TooltipProvider>{component}</TooltipProvider>);
};

describe("StreamingChat", () => {
  const mockSubscribe = vi.fn();
  const mockRunAgent = vi.fn();
  const mockUnsubscribe = vi.fn();

  let mockAgentMessages: Message[] = [];

  let capturedHandlers: any = null;

  const mockApiConfig = {
    basePath: "http://localhost:3000",
    accessToken: vi.fn().mockResolvedValue("mock-access-token"),
  } as any;

  beforeEach(() => {
    vi.clearAllMocks();
    mockAgentMessages = [];
    capturedHandlers = null;

    Element.prototype.scrollIntoView = vi.fn();

    vi.mocked(useAPIConfiguration).mockReturnValue(mockApiConfig);

    mockSubscribe.mockImplementation((handlers) => {
      capturedHandlers = handlers;
      return { unsubscribe: mockUnsubscribe };
    });

    const MockHttpAgentClass = vi.fn().mockImplementation(() => ({
      subscribe: mockSubscribe,
      runAgent: mockRunAgent.mockResolvedValue(undefined),
      get messages() {
        return mockAgentMessages;
      },
    }));

    vi.mocked(HttpAgent).mockImplementation(MockHttpAgentClass as any);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe("Component Rendering", () => {
    it("renders the Ask Zeplyn button", () => {
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      expect(button).toBeInTheDocument();
    });

    it("renders the Sparkles icon in the button", () => {
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      const svg = button.querySelector("svg");
      expect(svg).toBeInTheDocument();
    });

    it("opens modal when button is clicked", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      await waitFor(() => {
        expect(screen.getByRole("dialog")).toBeInTheDocument();
      });
    });

    it("displays modal with correct title", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      await waitFor(() => {
        expect(
          screen.getByRole("heading", { name: "Ask Zeplyn" })
        ).toBeInTheDocument();
      });
    });

    it("displays modal with correct description", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      await waitFor(() => {
        expect(
          screen.getByText(
            /Chat with Zeplyn AI which has access to your meeting notes/i
          )
        ).toBeInTheDocument();
      });
    });

    it("shows empty state message when no messages", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      await waitFor(() => {
        expect(
          screen.getByText(/Chat messages will appear here/i)
        ).toBeInTheDocument();
      });
    });

    it("focuses textarea after modal opens", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );

      await waitFor(
        () => {
          expect(textarea).toHaveFocus();
        },
        { timeout: 10000 }
      );
    });
  });

  describe("User Input", () => {
    it("textarea updates when user types", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Hello, Zeplyn!");

      expect(textarea).toHaveValue("Hello, Zeplyn!");
    });

    it("Send button is disabled when textarea is empty", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      await waitFor(() => {
        const sendButton = screen.getByRole("button", { name: /send/i });
        expect(sendButton).toBeDisabled();
      });
    });

    it("Send button is enabled when textarea has content", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test message");

      await waitFor(() => {
        const sendButton = screen.getByRole("button", { name: /send/i });
        expect(sendButton).toBeEnabled();
      });
    });

    it("Send button is disabled when textarea has only whitespace", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "   ");

      await waitFor(() => {
        const sendButton = screen.getByRole("button", { name: /send/i });
        expect(sendButton).toBeDisabled();
      });
    });

    it("textarea is disabled during streaming", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test query");

      const sendButton = screen.getByRole("button", { name: /send/i });
      await user.click(sendButton);

      await waitFor(() => {
        expect(textarea).toBeDisabled();
      });
    });

    it("handles Enter key to submit without Shift", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test query");

      await user.keyboard("{Enter}");

      // Should trigger submit - textarea should be cleared and query should be submitted
      await waitFor(() => {
        expect(textarea).toHaveValue("");
      });

      expect(HttpAgent).toHaveBeenCalled();
    });

    it("does not submit on Shift+Enter", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test query");

      await user.keyboard("{Shift>}{Enter}{/Shift}");

      expect(HttpAgent).not.toHaveBeenCalled();
    });

    it("handles error when Enter key submit fails", async () => {
      vi.mocked(useAPIConfiguration).mockReturnValue({
        basePath: "",
        accessToken: vi.fn().mockResolvedValue("mock-access-token"),
      } as any);

      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test query");

      await user.keyboard("{Enter}");

      await waitFor(() => {
        expect(HttpAgent).not.toHaveBeenCalled();
        expect(datadogLogs.logger.error).toHaveBeenCalledWith(
          "Error submitting query:",
          {},
          expect.any(Error)
        );
      });
    });
  });

  describe("HttpAgent Integration", () => {
    it("creates HttpAgent with correct URL when submitting", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test query");

      const sendButton = screen.getByRole("button", { name: /send/i });
      await user.click(sendButton);

      await waitFor(() => {
        expect(HttpAgent).toHaveBeenCalledWith(
          expect.objectContaining({
            url: expect.stringContaining(
              "http://localhost:3000/api/v2/search/ask"
            ),
          })
        );
      });
    });

    it("includes access token in URL params", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test query");

      const sendButton = screen.getByRole("button", { name: /send/i });
      await user.click(sendButton);

      await waitFor(() => {
        expect(HttpAgent).toHaveBeenCalledWith(
          expect.objectContaining({
            url: expect.stringContaining("access_token=mock-access-token"),
          })
        );
      });
    });

    it("includes noteId in URL params when provided", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat noteId="note-123" />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test query");

      const sendButton = screen.getByRole("button", { name: /send/i });
      await user.click(sendButton);

      await waitFor(() => {
        expect(HttpAgent).toHaveBeenCalledWith(
          expect.objectContaining({
            url: expect.stringContaining("note_id=note-123"),
          })
        );
      });
    });

    it("includes clientId in URL params when provided", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat clientId="client-456" />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test query");

      const sendButton = screen.getByRole("button", { name: /send/i });
      await user.click(sendButton);

      await waitFor(() => {
        expect(HttpAgent).toHaveBeenCalledWith(
          expect.objectContaining({
            url: expect.stringContaining("client_id=client-456"),
          })
        );
      });
    });

    it("passes initial messages to HttpAgent", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test query");

      const sendButton = screen.getByRole("button", { name: /send/i });
      await user.click(sendButton);

      await waitFor(() => {
        expect(HttpAgent).toHaveBeenCalledWith(
          expect.objectContaining({
            initialMessages: expect.arrayContaining([
              expect.objectContaining({
                role: "user",
                content: "Test query",
              }),
            ]),
          })
        );
      });
    });

    it("calls subscribe on HttpAgent", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test query");

      const sendButton = screen.getByRole("button", { name: /send/i });
      await user.click(sendButton);

      await waitFor(() => {
        expect(mockSubscribe).toHaveBeenCalledWith(
          expect.objectContaining({
            onTextMessageContentEvent: expect.any(Function),
            onToolCallStartEvent: expect.any(Function),
            onToolCallArgsEvent: expect.any(Function),
            onToolCallResultEvent: expect.any(Function),
            onRunErrorEvent: expect.any(Function),
            onRunFinishedEvent: expect.any(Function),
          })
        );
      });
    });

    it("calls runAgent after subscribing", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test query");

      const sendButton = screen.getByRole("button", { name: /send/i });
      await user.click(sendButton);

      await waitFor(() => {
        expect(mockRunAgent).toHaveBeenCalled();
      });
    });

    it("does not create HttpAgent when backend base path is empty", async () => {
      vi.mocked(useAPIConfiguration).mockReturnValue({
        basePath: "",
        accessToken: vi.fn().mockResolvedValue("mock-access-token"),
      } as any);

      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test query");

      const sendButton = screen.getByRole("button", { name: /send/i });

      await user.click(sendButton);

      await waitFor(() => {
        expect(HttpAgent).not.toHaveBeenCalled();
        expect(datadogLogs.logger.error).toHaveBeenCalledWith(
          "Error submitting query:",
          {},
          expect.any(Error)
        );
      });
    });
  });

  describe("Streaming Event Handlers", () => {
    it("handles onTextMessageContentEvent and displays text deltas", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test query");

      const sendButton = screen.getByRole("button", { name: /send/i });
      await user.click(sendButton);

      await waitFor(() => expect(mockSubscribe).toHaveBeenCalled());
      await waitFor(() => expect(capturedHandlers).toBeDefined());

      act(() => {
        capturedHandlers.onTextMessageContentEvent({
          event: { delta: "Hello " },
        });
        capturedHandlers.onTextMessageContentEvent({
          event: { delta: "world" },
        });
      });

      await waitFor(() => {
        expect(screen.getByText(/Hello world/)).toBeInTheDocument();
      });
    });

    it("handles onToolCallStartEvent and displays tool name", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test query");

      const sendButton = screen.getByRole("button", { name: /send/i });
      await user.click(sendButton);

      await waitFor(() => expect(mockSubscribe).toHaveBeenCalled());
      await waitFor(() => expect(capturedHandlers).toBeDefined());

      act(() => {
        capturedHandlers.onToolCallStartEvent({
          event: { toolCallName: "search_notes" },
        });
      });

      await waitFor(() => {
        expect(
          screen.getByText(/🔧 Calling tool: search_notes/)
        ).toBeInTheDocument();
      });
    });

    it("handles onToolCallArgsEvent and displays arguments", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test query");

      const sendButton = screen.getByRole("button", { name: /send/i });
      await user.click(sendButton);

      await waitFor(() => expect(mockSubscribe).toHaveBeenCalled());
      await waitFor(() => expect(capturedHandlers).toBeDefined());

      act(() => {
        capturedHandlers.onToolCallStartEvent({
          event: { toolCallName: "search" },
        });
        capturedHandlers.onToolCallArgsEvent({
          event: { delta: '{"query": ' },
        });
        capturedHandlers.onToolCallArgsEvent({ event: { delta: '"test"}' } });
      });

      await waitFor(() => {
        expect(screen.getByText(/\{"query": "test"\}/)).toBeInTheDocument();
      });
    });

    it("handles onToolCallResultEvent and displays result", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test query");

      const sendButton = screen.getByRole("button", { name: /send/i });
      await user.click(sendButton);

      await waitFor(() => expect(mockSubscribe).toHaveBeenCalled());
      await waitFor(() => expect(capturedHandlers).toBeDefined());

      act(() => {
        capturedHandlers.onToolCallResultEvent({
          event: { content: "Tool execution result" },
        });
      });

      await waitFor(() => {
        expect(screen.getByText(/→ Tool execution result/)).toBeInTheDocument();
      });
    });

    it("handles onRunErrorEvent and displays error", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test query");

      const sendButton = screen.getByRole("button", { name: /send/i });
      await user.click(sendButton);

      await waitFor(() => expect(mockSubscribe).toHaveBeenCalled());
      await waitFor(() => expect(capturedHandlers).toBeDefined());

      act(() => {
        capturedHandlers.onRunErrorEvent({
          event: { message: "Something went wrong" },
        });
      });

      await waitFor(() => {
        // Error is now formatted as markdown with emoji
        expect(screen.getByText(/⚠️/)).toBeInTheDocument();
        expect(screen.getByText(/Something went wrong/)).toBeInTheDocument();
      });

      await waitFor(() => {
        const sendButtonAfterError = screen.getByRole("button", {
          name: /send/i,
        });
        expect(sendButtonAfterError).toBeInTheDocument();
        expect(sendButtonAfterError).toBeDisabled();
      });

      await waitFor(() => {
        const textareaAfterError = screen.getByPlaceholderText(
          /Type your message here/i
        );
        expect(textareaAfterError).toBeDisabled();
      });
    });

    it("handles onRunFinishedEvent and moves response to messages", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test query");

      const sendButton = screen.getByRole("button", { name: /send/i });
      await user.click(sendButton);

      await waitFor(() => expect(mockSubscribe).toHaveBeenCalled());
      await waitFor(() => expect(capturedHandlers).toBeDefined());

      act(() => {
        capturedHandlers.onTextMessageContentEvent({
          event: { delta: "Response text" },
        });
        capturedHandlers.onRunFinishedEvent({});
      });

      await waitFor(() => {
        expect(screen.getByText("Response text")).toBeInTheDocument();
      });

      await waitFor(() => {
        const sendButtonAfter = screen.getByRole("button", { name: /send/i });
        expect(sendButtonAfter).toBeInTheDocument();
      });
    });

    it("renders tool messages from agent.messages array", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test query");

      const sendButton = screen.getByRole("button", { name: /send/i });
      await user.click(sendButton);

      await waitFor(() => expect(mockSubscribe).toHaveBeenCalled());
      await waitFor(() => expect(capturedHandlers).toBeDefined());

      // Set up agent.messages to include tool messages after runAgent completes
      mockAgentMessages.push(
        {
          id: "user-1",
          role: "user",
          content: "Test query",
        },
        {
          id: "assistant-1",
          role: "assistant",
          content: "Response with tool calls",
          toolCalls: [
            {
              id: "tool-1",
              type: "function",
              function: {
                name: "search_database",
                arguments: '{"query": "test"}',
              },
            },
          ],
        },
        {
          id: "tool-1",
          role: "tool",
          toolCallId: "tool-1",
          content: "Found 5 results",
        },
        {
          id: "assistant-2",
          role: "assistant",
          content: "Here are the results",
        }
      );

      act(() => {
        capturedHandlers.onRunFinishedEvent({});
      });

      await waitFor(() => {
        expect(
          screen.getByText(/Response with tool calls/)
        ).toBeInTheDocument();
        expect(screen.getByText(/Found 5 results/)).toBeInTheDocument();
        expect(screen.getByText(/Here are the results/)).toBeInTheDocument();
      });
    });
  });

  describe("Cancel Functionality", () => {
    it("shows Cancel button during streaming", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test query");

      const sendButton = screen.getByRole("button", { name: /send/i });
      await user.click(sendButton);

      await waitFor(() => {
        const cancelButton = screen.getByRole("button", { name: /cancel/i });
        expect(cancelButton).toBeInTheDocument();
      });
    });

    it("calls unsubscribe when Cancel is clicked", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test query");

      const sendButton = screen.getByRole("button", { name: /send/i });
      await user.click(sendButton);

      const cancelButton = await screen.findByRole("button", {
        name: /cancel/i,
      });
      await user.click(cancelButton);

      await waitFor(() => {
        expect(mockUnsubscribe).toHaveBeenCalled();
      });
    });

    it("adds canceled content to messages when Cancel is clicked", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test query");

      const sendButton = screen.getByRole("button", { name: /send/i });
      await user.click(sendButton);

      await waitFor(() => expect(mockSubscribe).toHaveBeenCalled());
      await waitFor(() => expect(capturedHandlers).toBeDefined());

      act(() => {
        capturedHandlers.onTextMessageContentEvent({
          event: { delta: "Partial response" },
        });
      });

      const cancelButton = await screen.findByRole("button", {
        name: /cancel/i,
      });
      await user.click(cancelButton);

      await waitFor(() => {
        expect(screen.getByText(/Partial response/)).toBeInTheDocument();
        expect(screen.getByText(/Canceled/)).toBeInTheDocument();
      });
    });

    it("stops streaming after Cancel is clicked", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test query");

      const sendButton = screen.getByRole("button", { name: /send/i });
      await user.click(sendButton);

      const cancelButton = await screen.findByRole("button", {
        name: /cancel/i,
      });
      await user.click(cancelButton);

      await waitFor(() => {
        const sendButtonAfter = screen.getByRole("button", { name: /send/i });
        expect(sendButtonAfter).toBeInTheDocument();
      });
    });
  });

  describe("Context Reset", () => {
    it("clears messages when noteId changes", async () => {
      const user = userEvent.setup();
      const { rerender } = renderWithTooltipProvider(
        <StreamingChat noteId="note-1" />
      );

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test query");

      const sendButton = screen.getByRole("button", { name: /send/i });
      await user.click(sendButton);

      await waitFor(() => expect(mockSubscribe).toHaveBeenCalled());
      await waitFor(() => expect(capturedHandlers).toBeDefined());

      // Simulate streaming response
      act(() => {
        capturedHandlers.onTextMessageContentEvent({
          event: { delta: "This is a response" },
        });
        capturedHandlers.onRunFinishedEvent({});
      });

      await waitFor(() => {
        expect(screen.getByText(/This is a response/)).toBeInTheDocument();
      });

      // Change noteId - should clear messages
      rerender(
        <TooltipProvider>
          <StreamingChat noteId="note-2" />
        </TooltipProvider>
      );

      await waitFor(() => {
        expect(
          screen.getByText(/Chat messages will appear here/i)
        ).toBeInTheDocument();
      });

      expect(screen.queryByText(/This is a response/)).not.toBeInTheDocument();
    });

    it("clears messages when clientId changes", async () => {
      const user = userEvent.setup();
      const { rerender } = renderWithTooltipProvider(
        <StreamingChat clientId="client-1" />
      );

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Another test query");

      const sendButton = screen.getByRole("button", { name: /send/i });
      await user.click(sendButton);

      await waitFor(() => expect(mockSubscribe).toHaveBeenCalled());
      await waitFor(() => expect(capturedHandlers).toBeDefined());

      // Simulate streaming response
      act(() => {
        capturedHandlers.onTextMessageContentEvent({
          event: { delta: "Response for client" },
        });
        capturedHandlers.onRunFinishedEvent({});
      });

      await waitFor(() => {
        expect(screen.getByText(/Response for client/)).toBeInTheDocument();
      });

      // Change clientId - should clear messages
      rerender(
        <TooltipProvider>
          <StreamingChat clientId="client-2" />
        </TooltipProvider>
      );

      await waitFor(() => {
        expect(
          screen.getByText(/Chat messages will appear here/i)
        ).toBeInTheDocument();
      });

      expect(screen.queryByText(/Response for client/)).not.toBeInTheDocument();
    });
  });

  describe("Cleanup", () => {
    it("unsubscribes on component unmount", async () => {
      const user = userEvent.setup();
      const { unmount } = renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test query");

      const sendButton = screen.getByRole("button", { name: /send/i });
      await user.click(sendButton);

      await waitFor(() => expect(mockSubscribe).toHaveBeenCalled());

      unmount();

      expect(mockUnsubscribe).toHaveBeenCalled();
    });
  });

  describe("Copy Functionality", () => {
    it("renders copy button for first assistant message after user", async () => {
      mockAgentMessages = [
        {
          id: "1",
          role: "user",
          content: "Hello",
        },
        {
          id: "2",
          role: "assistant",
          content: "Hi there! How can I help you?",
        },
      ];

      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test query");

      const sendButton = screen.getByRole("button", { name: /send/i });
      await user.click(sendButton);

      await waitFor(() => expect(mockSubscribe).toHaveBeenCalled());
      await waitFor(() => expect(capturedHandlers).toBeDefined());

      await act(async () => {
        capturedHandlers.onRunFinishedEvent();
        await mockRunAgent;
      });

      await waitFor(() => {
        expect(
          screen.getByText(/Hi there! How can I help you?/)
        ).toBeInTheDocument();
      });

      // Verify that a copy button exists
      await waitFor(() => {
        const copyButton = screen.getByTestId("copy-response-button");
        expect(copyButton).toBeDefined();
      });
    });
  });

  describe("Download Functionality", () => {
    it("downloads conversation as markdown when download button is clicked", async () => {
      const createElementSpy = vi.spyOn(document, "createElement");
      const appendChildSpy = vi.spyOn(document.body, "appendChild");
      const removeChildSpy = vi.spyOn(document.body, "removeChild");

      const createObjectURLMock = vi.fn().mockReturnValue("blob:mock-url");
      const revokeObjectURLMock = vi.fn();
      global.URL.createObjectURL = createObjectURLMock;
      global.URL.revokeObjectURL = revokeObjectURLMock;

      // Set up messages with various types
      mockAgentMessages = [
        {
          id: "1",
          role: "user",
          content: "What is the weather?",
        },
        {
          id: "2",
          role: "assistant",
          content: "Let me check the weather for you.",
        },
        {
          id: "3",
          role: "assistant",
          content: "",
          toolCalls: [
            {
              id: "tool-1",
              type: "function",
              function: {
                name: "get_weather",
                arguments: '{"location": "San Francisco"}',
              },
            },
          ],
        },
        {
          id: "4",
          role: "tool",
          content: "Temperature: 72°F, Sunny",
          toolCallId: "tool-1",
        },
        {
          id: "5",
          role: "assistant",
          content: "The weather in San Francisco is 72°F and sunny!",
        },
      ];

      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test query");

      const sendButton = screen.getByRole("button", { name: /send/i });
      await user.click(sendButton);

      await waitFor(() => expect(mockSubscribe).toHaveBeenCalled());
      await waitFor(() => expect(capturedHandlers).toBeDefined());

      await act(async () => {
        capturedHandlers.onRunFinishedEvent();
        await mockRunAgent;
      });

      await waitFor(() => {
        expect(
          screen.getByText(/Let me check the weather for you/)
        ).toBeInTheDocument();
      });

      // Wait for download button to appear - it shows when messages.length > 0
      // The download button should be in the footer
      let downloadButton: HTMLElement | undefined;
      await waitFor(() => {
        const allButtons = screen.getAllByRole("button");
        // Download button is not the Send button and has a Download icon (svg)
        downloadButton = allButtons.find((btn) => {
          return (
            btn !== sendButton &&
            btn.querySelector("svg.lucide-download, svg[class*='download']")
          );
        });
        expect(downloadButton).toBeDefined();
      });

      await user.click(downloadButton!);

      // Verify that a blob was created and download was triggered
      expect(createObjectURLMock).toHaveBeenCalledWith(expect.any(Blob));
      expect(createElementSpy).toHaveBeenCalledWith("a");
      expect(appendChildSpy).toHaveBeenCalled();
      expect(removeChildSpy).toHaveBeenCalled();
      expect(revokeObjectURLMock).toHaveBeenCalledWith("blob:mock-url");

      const anchorCall = createElementSpy.mock.results.find(
        (result) => result.value.tagName === "A"
      );
      expect(anchorCall).toBeDefined();
      if (anchorCall) {
        const anchor = anchorCall.value as HTMLAnchorElement;
        expect(anchor.href).toBe("blob:mock-url");
        expect(anchor.download).toMatch(/^zeplyn-conversation-.*\.md$/);
      }

      createElementSpy.mockRestore();
      appendChildSpy.mockRestore();
      removeChildSpy.mockRestore();
    });

    it("does not show download button when there are no messages", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      // Download button should not be visible when there are no messages
      // Check that there are no buttons in the footer area
      // (download button is conditional)
      const buttons = screen.getAllByRole("button");
      const downloadButton = buttons.find((btn) => {
        return btn.querySelector("svg") && btn.closest("footer");
      });
      expect(downloadButton).toBeUndefined();
    });

    it("includes run errors in downloaded markdown", async () => {
      const createElementSpy = vi.spyOn(document, "createElement");
      const createObjectURLMock = vi.fn().mockReturnValue("blob:mock-url");
      global.URL.createObjectURL = createObjectURLMock;

      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test query");

      const sendButton = screen.getByRole("button", { name: /send/i });
      await user.click(sendButton);

      await waitFor(() => expect(mockSubscribe).toHaveBeenCalled());
      await waitFor(() => expect(capturedHandlers).toBeDefined());

      // Simulate an error event
      await act(async () => {
        capturedHandlers.onRunErrorEvent({
          event: { message: "Something went wrong with the API" },
        });
      });

      describe("New Chat Button", () => {
        it("renders new chat button in the modal header", async () => {
          const user = userEvent.setup();
          renderWithTooltipProvider(<StreamingChat />);

          const button = screen.getByRole("button", { name: /ask zeplyn/i });
          await user.click(button);

          await waitFor(() => {
            const newChatButton = screen.getByRole("button", {
              name: /new chat/i,
            });
            expect(newChatButton).toBeInTheDocument();
          });
        });

        it("clears messages when new chat button is clicked", async () => {
          const user = userEvent.setup();
          renderWithTooltipProvider(<StreamingChat />);

          const button = screen.getByRole("button", { name: /ask zeplyn/i });
          await user.click(button);

          const textarea = await screen.findByPlaceholderText(
            /Type your message here/i
          );
          await user.type(textarea, "Test query");

          const sendButton = screen.getByRole("button", { name: /send/i });
          await user.click(sendButton);

          await waitFor(() => expect(mockSubscribe).toHaveBeenCalled());
          await waitFor(() => expect(capturedHandlers).toBeDefined());

          act(() => {
            capturedHandlers.onTextMessageContentEvent({
              event: { delta: "This is a response" },
            });
            capturedHandlers.onRunFinishedEvent({});
          });

          await waitFor(() => {
            expect(screen.getByText(/This is a response/)).toBeInTheDocument();
          });

          const newChatButton = screen.getByRole("button", {
            name: /new chat/i,
          });
          await user.click(newChatButton);

          await waitFor(() => {
            expect(
              screen.getByText(/Chat messages will appear here/i)
            ).toBeInTheDocument();
          });

          expect(
            screen.queryByText(/This is a response/)
          ).not.toBeInTheDocument();
        });

        it("does not affect the modal open state when clicked", async () => {
          const user = userEvent.setup();
          renderWithTooltipProvider(<StreamingChat />);

          const button = screen.getByRole("button", { name: /ask zeplyn/i });
          await user.click(button);

          await waitFor(() => {
            expect(screen.getByRole("dialog")).toBeInTheDocument();
          });

          const newChatButton = screen.getByRole("button", {
            name: /new chat/i,
          });
          await user.click(newChatButton);

          // Modal should still be open
          await waitFor(() => {
            expect(screen.getByRole("dialog")).toBeInTheDocument();
          });
        });
      });

      await waitFor(() => {
        // Error is now formatted as markdown with emoji
        expect(screen.getByText(/⚠️/)).toBeInTheDocument();
        expect(
          screen.getByText(/Something went wrong with the API/)
        ).toBeInTheDocument();
      });

      let downloadButton: HTMLElement | undefined;
      await waitFor(() => {
        const allButtons = screen.getAllByRole("button");
        downloadButton = allButtons.find((btn) => {
          return (
            btn !== sendButton &&
            btn.querySelector("svg.lucide-download, svg[class*='download']")
          );
        });
        expect(downloadButton).toBeDefined();
      });

      await user.click(downloadButton!);

      expect(createObjectURLMock).toHaveBeenCalledWith(expect.any(Blob));

      const blobConstructorCalls = createObjectURLMock.mock.calls.filter(
        (call) => call[0] instanceof Blob
      );
      expect(blobConstructorCalls.length).toBeGreaterThan(0);

      const blob = blobConstructorCalls[0]?.[0];
      expect(blob).toBeDefined();

      const blobText = await new Promise<string>((resolve) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.readAsText(blob!);
      });
      expect(blobText).toContain(
        "⚠️ **Error**: Something went wrong with the API"
      );

      createElementSpy.mockRestore();
    });

    it("includes tool call errors in downloaded markdown", async () => {
      const createElementSpy = vi.spyOn(document, "createElement");
      const createObjectURLMock = vi.fn().mockReturnValue("blob:mock-url");
      global.URL.createObjectURL = createObjectURLMock;

      // Set up messages with a tool call but missing tool result
      mockAgentMessages = [
        {
          id: "1",
          role: "user",
          content: "Test query",
        },
        {
          id: "2",
          role: "assistant",
          content: "Let me search for that",
          toolCalls: [
            {
              id: "tool-1",
              type: "function",
              function: {
                name: "test_tool",
                arguments: '{"arg": "value"}',
              },
            },
          ],
        },
      ];

      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test query");

      const sendButton = screen.getByRole("button", { name: /send/i });
      await user.click(sendButton);

      await waitFor(() => expect(mockSubscribe).toHaveBeenCalled());
      await waitFor(() => expect(capturedHandlers).toBeDefined());

      await act(async () => {
        capturedHandlers.onRunFinishedEvent();
        await mockRunAgent;
        await new Promise((resolve) =>
          requestAnimationFrame(() => resolve(undefined))
        );
      });

      await waitFor(() => {
        expect(
          screen.getByText(/Tool call result missing for tool call ID: tool-1/)
        ).toBeInTheDocument();
      });

      let downloadButton: HTMLElement | undefined;
      await waitFor(() => {
        const allButtons = screen.getAllByRole("button");
        downloadButton = allButtons.find((btn) => {
          return (
            btn !== sendButton &&
            btn.querySelector("svg.lucide-download, svg[class*='download']")
          );
        });
        expect(downloadButton).toBeDefined();
      });

      await user.click(downloadButton!);

      expect(createObjectURLMock).toHaveBeenCalledWith(expect.any(Blob));

      const blobConstructorCalls = createObjectURLMock.mock.calls.filter(
        (call) => call[0] instanceof Blob
      );
      expect(blobConstructorCalls.length).toBeGreaterThan(0);

      const blob = blobConstructorCalls[0]?.[0];
      expect(blob).toBeDefined();

      const blobText = await new Promise<string>((resolve) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.readAsText(blob!);
      });
      expect(blobText).toContain(
        "⚠️ **Error**: Tool call result missing for tool call ID: tool-1"
      );

      createElementSpy.mockRestore();
    });
  });

  describe("Error Handling", () => {
    it("shows error message for tool call without following tool response", async () => {
      const user = userEvent.setup();
      renderWithTooltipProvider(<StreamingChat />);

      const button = screen.getByRole("button", { name: /ask zeplyn/i });
      await user.click(button);

      const textarea = await screen.findByPlaceholderText(
        /Type your message here/i
      );
      await user.type(textarea, "Test query");

      const sendButton = screen.getByRole("button", { name: /send/i });
      await user.click(sendButton);

      await waitFor(() => expect(mockSubscribe).toHaveBeenCalled());
      await waitFor(() => expect(capturedHandlers).toBeDefined());

      // Set up malformed messages - assistant message with toolCalls but no following tool response
      mockAgentMessages.push(
        {
          id: "1",
          role: "user",
          content: "Test query",
        },
        {
          id: "2",
          role: "assistant",
          content: "Response with tool calls",
          toolCalls: [
            {
              id: "tool-1",
              type: "function",
              function: {
                name: "test_tool",
                arguments: '{"arg": "value"}',
              },
            },
          ],
        }
      );

      await act(async () => {
        capturedHandlers.onRunFinishedEvent();
        await mockRunAgent;
        await new Promise((resolve) =>
          requestAnimationFrame(() => resolve(undefined))
        );
      });

      await waitFor(() => {
        expect(
          screen.getByText(
            /Error: Tool call result missing for tool call ID: tool-1/
          )
        ).toBeInTheDocument();
      });

      await waitFor(() => {
        expect(datadogLogs.logger.error).toHaveBeenCalledWith(
          "Tool call result missing for tool call ID: tool-1",
          expect.objectContaining({
            message: expect.objectContaining({
              id: "2",
              role: "assistant",
            }),
            index: 1,
            toolCall: expect.objectContaining({
              id: "tool-1",
            }),
          })
        );
      });

      // Check that the textarea and send button are disabled after error
      await waitFor(() => {
        const textareaAfterError = screen.getByPlaceholderText(
          /Type your message here/i
        );
        expect(textareaAfterError).toBeDisabled();
      });

      await waitFor(() => {
        const sendButtonAfterError = screen.getByRole("button", {
          name: /send/i,
        });
        expect(sendButtonAfterError).toBeDisabled();
      });
    });
  });
});
