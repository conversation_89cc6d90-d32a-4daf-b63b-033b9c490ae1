import { useState, useRef, useEffect } from "react";
import { Spark<PERSON>, Download, Copy, Plus } from "lucide-react";
import { v4 as uuidv4 } from "uuid";
import { datadogLogs } from "@datadog/browser-logs";
import ReactMarkdown from "react-markdown";
import { Button } from "~/@shadcn/ui/button";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from "~/@shadcn/ui/sheet";
import { TextareaGrowable } from "~/@shadcn/ui/textarea";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";
import { HttpAgent, type Message } from "@ag-ui/client";
import { useAPIConfiguration } from "~/context/apiAuth";

const UserMessageComponent = ({ content }: { content: string }) => {
  return (
    <div className="my-3 ml-auto max-w-[80%] pr-2">
      <div className="relative rounded-lg bg-muted p-4 text-foreground shadow-sm">
        <div className="whitespace-pre-wrap text-sm">{content}</div>
        <div className="absolute right-0 top-4 -mr-2 h-0 w-0 border-b-8 border-l-8 border-t-8 border-b-transparent border-l-muted border-t-transparent"></div>
      </div>
    </div>
  );
};

// This component is used while a response is still streaming.
const StreamingResponseComponent = ({ content }: { content: string }) => {
  return <div className="whitespace-pre-wrap text-sm">{content}</div>;
};

// These components are used when the streaming is finished.
const ResponseTextComponent = ({ content }: { content: string }) => {
  return (
    <div className="prose prose-sm max-w-none dark:prose-invert">
      <ReactMarkdown>{content}</ReactMarkdown>
    </div>
  );
};

interface ToolCallComponentProps {
  toolCalls: Array<{
    id: string;
    type: string;
    function: {
      name: string;
      arguments: string;
    };
  }>;
  toolResults: Message[];
}

const ToolCallComponent = ({
  toolCalls,
  toolResults,
}: ToolCallComponentProps) => {
  return (
    <div className="my-2 space-y-2">
      {toolCalls.map((toolCall, idx) => {
        const toolResult = toolResults[idx];

        return (
          <div
            key={toolCall.id}
            className="rounded-md border border-border bg-muted/50 p-3"
          >
            <div className="mb-2 text-sm font-medium">
              🔧 Calling tool: {toolCall.function.name}
            </div>
            <pre className="mb-2 whitespace-pre-wrap text-xs">
              {toolCall.function.arguments}
            </pre>
            {toolResult && (
              <>
                <div className="my-2 border-t border-border" />
                <div className="text-sm">
                  <span className="mr-2">→</span>
                  <span className="whitespace-pre-wrap">
                    {toolResult.content}
                  </span>
                </div>
              </>
            )}
          </div>
        );
      })}
    </div>
  );
};

interface GenericMessageComponentProps {
  message: Message;
  index: number;
  messages: Message[];
  setHasError: (hasError: boolean) => void;
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>;
}

const GenericMessageComponent = ({
  message,
  index,
  messages,
  setHasError,
  setMessages,
}: GenericMessageComponentProps) => {
  if (message.role === "user") {
    return <UserMessageComponent key={message.id} content={message.content} />;
  }

  // We want to group tool calls and tool responses in the same node, so
  // jump through some hoops to do so.
  if (message.role === "tool") {
    // Skip tool messages (they'll be rendered inline with the tool call)
    return null;
  }

  const hasToolCalls =
    message.role === "assistant" && Boolean(message.toolCalls?.length);

  const toolResultsMap = new Map<string, Message>();
  const toolResults: Message[] = [];
  if (hasToolCalls) {
    // Find all consecutive tool messages after this assistant message
    const subsequentMessages = messages.slice(index + 1);
    for (const nextMsg of subsequentMessages) {
      if (nextMsg.role === "tool") {
        if ("toolCallId" in nextMsg) {
          toolResultsMap.set(nextMsg.toolCallId as string, nextMsg);
        }
      } else {
        break;
      }
    }

    // Build ordered list of results matching the order of tool calls
    for (const toolCall of message.toolCalls!) {
      const result = toolResultsMap.get(toolCall.id);
      if (!result) {
        const errorMsg = `Tool call result missing for tool call ID: ${toolCall.id}`;
        datadogLogs.logger.error(errorMsg, {
          message,
          index,
          messages,
          toolCall,
        });

        // Since there was no correct tool result message, we can add
        // a tool result message with the error to the messages state.
        // Then it will render like a normal tool call, but show the
        // error in the UI where the result would have been.
        // We have to use setTimeout to avoid React state update
        // during render errors.
        setTimeout(() => {
          setHasError(true);
          setMessages((prev) => [
            ...prev,
            {
              id: uuidv4(),
              role: "tool",
              content: `⚠️ **Error**: ${errorMsg}`,
              toolCallId: toolCall.id,
            } as Message,
          ]);
        }, 0);

        return (
          <div className="my-2 rounded-md border border-destructive bg-destructive/10 p-3 text-sm text-destructive">
            Error: {errorMsg}
          </div>
        );
      }
      toolResults.push(result);
    }
  }

  // If this is the first message after the user's message, collect all messages
  // until the next user message for the copy to clipboard feature.
  const isFirstAfterUser = index > 0 && messages[index - 1]?.role === "user";

  let contentToCopy = "";
  if (isFirstAfterUser) {
    for (const msg of messages.slice(index)) {
      if (!msg || msg.role === "user") break;

      if (msg.role === "assistant") {
        contentToCopy += msg.content ?? "";
        if (
          msg.role === "assistant" &&
          msg.toolCalls &&
          msg.toolCalls.length > 0
        ) {
          for (const toolCall of msg.toolCalls) {
            contentToCopy += `\n\n 🔧 Calling tool: ${toolCall.function.name}\n`;
            contentToCopy += toolCall.function.arguments;
            contentToCopy += "\n → ";
            contentToCopy += toolResultsMap.get(toolCall.id)?.content ?? "";
          }
          contentToCopy += "\n\n";
        }
      }
    }
  }

  return (
    <>
      <AssistantMessageComponent
        key={message.id}
        message={message}
        contentToCopy={contentToCopy}
      />
      {hasToolCalls && (
        <ToolCallComponent
          toolCalls={message.toolCalls!}
          toolResults={toolResults}
        />
      )}
    </>
  );
};

const AssistantMessageComponent = ({
  message,
  contentToCopy,
}: {
  message: Message;
  contentToCopy: string;
}) => {
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(
        contentToCopy || message.content || ""
      );
    } catch (error) {
      datadogLogs.logger.error(
        "Failed to copy to clipboard:",
        {},
        error as Error
      );
    }
  };

  return (
    <div className="group relative">
      {message.content && <ResponseTextComponent content={message.content} />}

      {Boolean(contentToCopy.length) && (
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              onClick={handleCopy}
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 bg-background opacity-0 transition-opacity group-hover:opacity-100"
              data-testid="copy-response-button"
            >
              <Copy className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Copy response</TooltipContent>
        </Tooltip>
      )}
    </div>
  );
};

interface StreamingChatProps {
  noteId?: string;
  clientId?: string;
}

const StreamingChat = ({ noteId, clientId }: StreamingChatProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [query, setQuery] = useState("");
  const [messages, setMessages] = useState<Message[]>([]);
  const [currentResponse, setCurrentResponse] = useState("");
  const [isStreaming, setIsStreaming] = useState(false);
  const [hasError, setHasError] = useState(false);
  const apiConfig = useAPIConfiguration();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const unsubscribeRef = useRef<{ unsubscribe: () => void } | null>(null);
  const currentResponseRef = useRef<string>("");
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Reset messages when noteId or clientId changes (new chat context)
  useEffect(() => {
    setMessages([]);
    setCurrentResponse("");
    currentResponseRef.current = "";
    setHasError(false);
  }, [noteId, clientId]);

  useEffect(() => {
    // Scroll to bottom when messages or current response changes
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages, currentResponse]);

  useEffect(() => {
    // When using useEffect with no dependencies, returning a function
    // causes the function to be called when the component is unmounted.
    // Call unsubscribe in case there's still an outstanding streaming chat.
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current.unsubscribe();
      }
    };
  }, []);

  const handleSubmit = async () => {
    const userQuery = query.trim();

    const userMessage: Message = {
      id: uuidv4(),
      role: "user",
      content: userQuery,
    };

    setIsStreaming(true);
    setCurrentResponse("");
    setQuery("");
    setMessages((prev) => [...prev, userMessage]);

    const backendBaseUrl = apiConfig?.basePath;
    if (!backendBaseUrl) {
      throw new Error("Backend base path not configured.");
    }
    const params = new URLSearchParams();
    const accessTokenFn = apiConfig?.accessToken;
    if (accessTokenFn) {
      const token = await accessTokenFn();
      params.append("access_token", token);
    }
    if (noteId) params.append("note_id", noteId);
    if (clientId) params.append("client_id", clientId);

    const url = `${backendBaseUrl}/api/v2/search/ask?${params.toString()}`;
    const agent = new HttpAgent({
      url,
      initialMessages: [...messages, userMessage],
    });

    unsubscribeRef.current = agent.subscribe({
      onTextMessageContentEvent: (params) => {
        if (params.event.delta) {
          setCurrentResponse((prev) => {
            const newValue = prev + params.event.delta;
            currentResponseRef.current = newValue;
            return newValue;
          });
        }
      },

      onToolCallStartEvent: (params) => {
        setCurrentResponse((prev) => {
          const newValue =
            prev + `\n\n 🔧 Calling tool: ${params.event.toolCallName}\n`;
          currentResponseRef.current = newValue;
          return newValue;
        });
      },

      onToolCallArgsEvent: (params) => {
        if (params.event.delta) {
          setCurrentResponse((prev) => {
            const newValue = prev + params.event.delta;
            currentResponseRef.current = newValue;
            return newValue;
          });
        }
      },

      onToolCallResultEvent: (params) => {
        setCurrentResponse((prev) => {
          const newValue = prev + `\n → ${params.event.content}\n\n`;
          currentResponseRef.current = newValue;
          return newValue;
        });
      },

      onRunErrorEvent: (params) => {
        setCurrentResponse(
          (prev) => prev + `\n\nError: ${params.event.message}\n\n`
        );
        const errorMessageId = uuidv4();
        setMessages((prev) => [
          ...prev,
          {
            id: errorMessageId,
            role: "assistant",
            content:
              currentResponseRef.current +
              `\n\n> ⚠️ **Error**: ${params.event.message}\n\n`,
          },
        ]);
        setCurrentResponse("");
        currentResponseRef.current = "";
        setIsStreaming(false);
        setHasError(true);
      },

      onRunFinishedEvent: () => {
        const finalContent = currentResponseRef.current;
        if (finalContent) {
          setMessages((prev) => [
            ...prev,
            {
              id: uuidv4(),
              role: "assistant",
              content: finalContent,
            },
          ]);
        }
        setCurrentResponse("");
        currentResponseRef.current = "";
        setIsStreaming(false);
        if (unsubscribeRef.current) {
          unsubscribeRef.current.unsubscribe();
          unsubscribeRef.current = null;
        }
        // Focus the textarea after the run completes
        // Use requestAnimationFrame to ensure the textarea is re-enabled
        // before focusing
        requestAnimationFrame(() => {
          textareaRef.current?.focus();
        });
      },
    });

    await agent.runAgent();
    // The runAgent promise doesn't resolve until after onRunFinishedEvent.
    // We want to set our state to the exact agent.messages instead of the
    // ones we created during streaming so we can pass the exact messages
    // from the last agent instance to the next agent instance created
    // in the next call to handleSubmit.

    // This will also replace the plain streaming format with rich html
    // for tool calls and Markdown agent responses.
    setMessages((_prev) => agent.messages);
  };

  const handleCancel = () => {
    if (unsubscribeRef.current) {
      unsubscribeRef.current.unsubscribe();
      unsubscribeRef.current = null;
    }
    setIsStreaming(false);
    setCurrentResponse((prev) => {
      const canceledContent = prev ? prev + "\n\nCanceled" : "Canceled";
      setMessages((msgs) => [
        ...msgs,
        {
          id: uuidv4(),
          role: "assistant",
          content: canceledContent,
        },
      ]);
      return "";
    });
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      if (!isStreaming && !hasError && query.trim()) {
        handleSubmit().catch((error) => {
          datadogLogs.logger.error("Error submitting query:", {}, error);
          setIsStreaming(false);
          setHasError(true);
        });
      }
    }
  };

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      const text = await file.text();
      setQuery((prev) => (prev ? prev + "\n\n" + text : text));
    } catch (error) {
      datadogLogs.logger.error("Failed to read file:", {}, error as Error);
    }

    // Reset the input so the same file can be selected again
    e.target.value = "";
  };

  const handleDownloadConversation = (e: React.MouseEvent) => {
    e.preventDefault();

    let markdown = "";
    let lastRole = "";

    const toolResultsMap = new Map<string, Message>();
    messages.forEach((message) => {
      if (message.role === "tool" && "toolCallId" in message) {
        toolResultsMap.set(message.toolCallId as string, message);
      }
    });
    messages.forEach((message) => {
      if (message.role === "user") {
        if (lastRole !== "user") {
          markdown += `## User\n\n${message.content}\n\n`;
        }
        lastRole = "user";
      } else if (message.role === "assistant") {
        // Only add "Assistant" header if the last message wasn't also
        // from assistant
        if (lastRole === "user") {
          markdown += `## Assistant\n\n`;
        }
        if (message.content) {
          markdown += `${message.content}\n\n`;
        }
        if (message.toolCalls && message.toolCalls.length > 0) {
          message.toolCalls.forEach((toolCall) => {
            markdown += ` 🔧 **${toolCall.function.name}**\n\n`;
            markdown += `\`\`\`json\n${toolCall.function.arguments}\n\`\`\`\n\n`;
            const result = toolResultsMap.get(toolCall.id);
            if (result) {
              markdown += ` → ${result.content}\n\n`;
            }
          });
        }
        lastRole = "assistant";
      }
    });

    const blob = new Blob([markdown], { type: "text/markdown" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    a.download = `zeplyn-conversation-${timestamp}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const sendOrCancelButton = isStreaming ? (
    <Button
      onClick={handleCancel}
      variant="outline"
      className="z-[9999999999] m-1 hover:bg-destructive hover:text-destructive-foreground"
    >
      Cancel
    </Button>
  ) : (
    <Button
      onClick={() => {
        handleSubmit().catch((error) => {
          datadogLogs.logger.error("Error submitting query:", {}, error);
          setIsStreaming(false);
          setHasError(true);
        });
      }}
      variant="default"
      disabled={!query.trim() || hasError}
      className="z-[9999999999] m-1"
    >
      Send
    </Button>
  );

  return (
    <div>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button variant="outline_magic" onClick={() => setIsModalOpen(true)}>
            <Sparkles className="!h-5 !w-5" />{" "}
            <span className="bg-magic hidden bg-clip-text text-transparent sm:block">
              Ask Zeplyn
            </span>
          </Button>
        </TooltipTrigger>
        <TooltipContent>Ask Zeplyn</TooltipContent>
      </Tooltip>

      <Sheet open={isModalOpen} onOpenChange={setIsModalOpen}>
        <SheetContent className="z-[9999999999] flex w-full flex-col sm:w-2/5 sm:max-w-none">
          <SheetHeader>
            <div className="flex">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    onClick={() => setMessages([])}
                    variant="outline"
                    size="sm"
                    className="mr-2"
                    aria-label="New Chat"
                    disabled={isStreaming}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>New Chat</TooltipContent>
              </Tooltip>
              <SheetTitle className="magic-gradient-text flex items-center">
                <Sparkles className="mr-2" style={{ color: "#2980b9" }} />
                <span>Ask Zeplyn</span>
              </SheetTitle>
            </div>
            <SheetDescription>
              Chat with Zeplyn AI which has access to your meeting notes and
              client notes.
            </SheetDescription>
          </SheetHeader>

          <div className="flex grow flex-col justify-start gap-4 overflow-auto">
            {messages.length == 0 && !currentResponse ? (
              <p className="text-sm text-muted-foreground">
                Chat messages will appear here...
              </p>
            ) : (
              <div className="text-sm">
                {messages.map((message, index) => (
                  <GenericMessageComponent
                    key={message.id}
                    message={message}
                    index={index}
                    messages={messages}
                    setHasError={setHasError}
                    setMessages={setMessages}
                  />
                ))}
                {currentResponse && (
                  <StreamingResponseComponent content={currentResponse} />
                )}
                <div ref={messagesEndRef} />
              </div>
            )}
          </div>

          <SheetFooter>
            <TextareaGrowable
              ref={textareaRef}
              placeholder="Type your message here..."
              variant="outline"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyDown={handleKeyDown}
              disabled={isStreaming || hasError}
              autoFocus
              className="m-1"
            />
            <div className="flex shrink-0 flex-col justify-end gap-1">
              <input
                ref={fileInputRef}
                type="file"
                accept=".txt,text/plain"
                onChange={handleFileChange}
                className="hidden"
              />
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    onClick={handleFileSelect}
                    variant="ghost"
                    size="sm"
                    disabled={isStreaming}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Attach text file</TooltipContent>
              </Tooltip>
              {messages.length > 0 && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      onClick={handleDownloadConversation}
                      variant="ghost"
                      size="sm"
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    Download conversation as Markdown
                  </TooltipContent>
                </Tooltip>
              )}
              {sendOrCancelButton}
            </div>
          </SheetFooter>
        </SheetContent>
      </Sheet>
    </div>
  );
};

export default StreamingChat;
