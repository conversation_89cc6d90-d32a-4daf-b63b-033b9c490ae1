import { toast } from "react-toastify";

import { But<PERSON> } from "~/@shadcn/ui/button";
import { Popover, PopoverTrigger, PopoverContent } from "~/@shadcn/ui/popover";
import { cn } from "~/@shadcn/utils";
import { PlusIcon, CheckIcon, CaretSortIcon } from "@radix-ui/react-icons";
import { Search } from "lucide-react";
import { Typography } from "~/@ui/Typography";
import { Input } from "~/@shadcn/ui/input";
import { Separator } from "~/@shadcn/ui/separator";
import { AttendeeOption, AttendeeOptions } from "~/api/attendees/types";
import { AttendeeOptionTag } from "./attendeeTags";
import {
  ReactNode,
  Dispatch,
  SetStateAction,
  useState,
  useRef,
  useMemo,
  useEffect,
  useCallback,
  MouseEventHandler,
} from "react";
import { useFuzzySearchList, Highlight } from "@nozbe/microfuzz/react.js";
import { useFlag } from "~/context/flags";
import { FuzzyMatches } from "@nozbe/microfuzz";
import { FixedSizeList } from "react-window";
import CreateEditClientFlow from "../clients/CreateEditClientFlow";

type Props = {
  initialOptions: AttendeeOption[];
  selected: AttendeeOptions;
  placeholder?: string;
  emptyLabel?: string;
  leftIcon?: ReactNode;
  onChange: Dispatch<SetStateAction<AttendeeOptions>>;
  commandClassName?: string;
  triggerClassName?: string;
  allowNew?: boolean;
  modal?: boolean;
  "aria-describedby"?: string;
  maxListHeightPixels?: number;
};

const attendeeTagTitle = (
  attendee: AttendeeOption,
  showClientType: boolean
) => {
  let title: string | undefined = attendee.type;
  if (!title) {
    return "";
  }
  if (
    showClientType &&
    attendee.type === "client" &&
    attendee.clientType &&
    attendee.clientType.toLowerCase() !== "unknown"
  ) {
    title = title.concat(` (${attendee.clientType})`);
  }
  return title;
};

// A button used for each attendee option in the list
//
// The ShadCN button component adds a lot of classes that are not helpful here; this component
// includes only the ones that are necessary for this component to work correctly.
const AttendeeButton = ({
  className,
  title,
  value,
  onClick,
  children,
}: {
  className: string;
  title: string;
  value: string;
  onClick: MouseEventHandler<HTMLButtonElement>;
  children: React.ReactNode;
}) => (
  <button
    title={title}
    value={value}
    onClick={onClick}
    className={cn(
      "flex h-full w-full flex-row items-center justify-between overflow-hidden rounded-md px-2 font-medium transition-colors hover:bg-accent hover:text-accent-foreground",
      className
    )}
  >
    {children}
  </button>
);

export const AttendeesMultiSelect = ({
  initialOptions,
  selected,
  placeholder = "Select an item",
  emptyLabel = "No item found.",
  leftIcon,
  onChange,
  commandClassName,
  triggerClassName,
  allowNew = true,
  modal = false,
  "aria-describedby": ariaDescribedBy,
  maxListHeightPixels = 400, // maximum height of the list before it starts scrolling, in pixels.
}: Props) => {
  const [open, setOpen] = useState(false);
  const [options, setOptions] = useState<AttendeeOptions>(initialOptions);
  const [searchTerm, setSearchTerm] = useState("");

  const [isAddClientFormOpen, setIsAddClientFormOpen] = useState(false);
  const [
    selectedAttendeeForClientCreation,
    setSelectedAttendeeForClientCreation,
  ] = useState<AttendeeOption | null>(null); // this data will be used to update the dropdown after client creation

  // NOTE: storing only a single text value as prefilled data which can be either name or email
  const [prefilledData, setPrefilledData] = useState("");

  const isClientCreationEnabled = useFlag("EnableClientCreation");

  const searchInputRef = useRef<HTMLInputElement>(null);

  // The height of each item in the virtualized list, in pixels
  const itemSize = 54;

  const mapResultItem = useCallback(
    ({
      item,
      score,
      matches: [highlightRanges],
    }: {
      item: AttendeeOption;
      score: number;
      matches: FuzzyMatches;
    }) => ({
      item,
      highlightRanges,
    }),
    []
  );

  const filteredOptions = useFuzzySearchList({
    list: options,
    queryText: searchTerm,
    key: "name",
    mapResultItem,
  });

  const searchTermTrimmed = searchTerm.trim();

  const clearInputAndFocus = () => {
    // Clear search input, then focus it again
    if (searchInputRef.current) {
      setSearchTerm("");
      searchInputRef.current.value = "";
      searchInputRef.current.focus();
    }
  };

  const addNewOption = () => {
    const newOptionToAdd: AttendeeOption = {
      name: searchTermTrimmed,
      uuid: crypto.randomUUID(),
      type: "unknown",
    };
    setOptions((prev) => prev.concat(newOptionToAdd));
    onChange([...selected, newOptionToAdd]);
    setOpen(true);
  };

  const toggleOptionSelection = (option: AttendeeOption) => {
    const wasSelected = selected.find((item) => item.uuid === option.uuid);
    onChange(
      wasSelected
        ? selected.filter((item) => item.uuid !== option.uuid)
        : selected.concat(option)
    );
    return !wasSelected;
  };

  const showAddNewOption = useMemo(() => {
    if (!allowNew) return false;
    // Only show the "Add '<searchTerm>'" option when:
    // 1. We have a valid search term string
    if (searchTermTrimmed.length === 0) return false;
    // 2. The to-be-added option doesn't already exist
    if (selected.find((item) => item.name == searchTermTrimmed)) return false;
    if (
      options.find((option) => {
        const searchTermLower = searchTermTrimmed.toLocaleLowerCase();
        return (
          option.name.toLocaleLowerCase() === searchTermLower ||
          option.uuid.toLocaleLowerCase() === searchTermLower
        );
      })
    ) {
      return false;
    }
    return true;
  }, [allowNew, options, searchTermTrimmed, selected]);

  const [cursor, setCursor] = useState(0);
  const cursorRange = filteredOptions.length + (showAddNewOption ? 1 : 0);

  const moveCursorUp = () => {
    setCursor((prev) => {
      if (prev === 0) return prev;
      return prev - 1;
    });
  };
  const moveCursorDown = () => {
    setCursor((prev) => {
      if (prev + 1 >= cursorRange) return prev;
      return prev + 1;
    });
  };

  useEffect(() => {
    setCursor(0);
  }, [searchTerm]);

  const hasMultipleClientTypes =
    new Set(
      options.flatMap((option) =>
        option.type === "client" ? [option.clientType] : []
      )
    ).size > 1;

  // Row renderer for virtualized list
  const OptionRow = ({
    index,
    style,
  }: {
    index: number;
    style: React.CSSProperties;
  }) => {
    // If we're at the end and have the "add new" option
    if (showAddNewOption && index === filteredOptions.length) {
      const title = `Add "${searchTermTrimmed}"`;
      return (
        <div style={style}>
          <AttendeeButton
            className={cn(index === cursor && "bg-accent")}
            title={title}
            value={searchTerm}
            onClick={() => {
              addNewOption();
              clearInputAndFocus();
            }}
          >
            <PlusIcon />
            <span className="grow truncate px-2 text-start">{title}</span>
          </AttendeeButton>
        </div>
      );
    }

    // Regular option
    const option = filteredOptions[index];
    if (!option) return null;

    const item = option.item;

    return (
      <div style={style}>
        <AttendeeButton
          title={item.name}
          key={item.uuid}
          className={cn(index === cursor && "bg-accent")}
          value={item.uuid}
          onClick={() => {
            const didSelect = toggleOptionSelection(item);
            if (didSelect) {
              clearInputAndFocus();
            }
            setOpen(true);
            // Ensure that the search input is focused after selecting an option, so the user
            // can continue searching for more attendees.
            if (searchInputRef.current) {
              searchInputRef.current.focus();
            }
          }}
        >
          <div className="flex min-w-0">
            <div className="flex min-w-0 flex-col items-start">
              <span className="max-w-full truncate">
                <Highlight
                  ranges={option.highlightRanges ?? null}
                  text={item.name}
                />
              </span>
              <Typography className="text-sm text-secondary first-letter:uppercase">
                {attendeeTagTitle(item, hasMultipleClientTypes)}
              </Typography>
            </div>
          </div>
          {selected.find((i) => i.uuid === item.uuid) && (
            <CheckIcon className="shrink-0" />
          )}
        </AttendeeButton>
      </div>
    );
  };

  // Calculate the total number of items to render (filtered options + possibly the add new option)
  const totalItemCount = filteredOptions.length + (showAddNewOption ? 1 : 0);

  // Calculate the height for the virtualized list
  const listHeight = Math.min(totalItemCount * itemSize, maxListHeightPixels);

  // client creation
  const openClientCreationFlow = (selectedOption: AttendeeOption) => {
    setPrefilledData(selectedOption.name);
    setSelectedAttendeeForClientCreation(selectedOption);
    setIsAddClientFormOpen(true);
  };

  const onClientCreation = (
    clientUuid: string,
    formData: Record<string, any>
  ) => {
    // selectedAttendeeForClientCreation should never be null here, but just in case
    if (!selectedAttendeeForClientCreation) {
      return;
    }

    // update the list of options
    setOptions((prev) => {
      return prev.map((option) => {
        if (option.uuid === selectedAttendeeForClientCreation.uuid) {
          return {
            ...option,
            uuid: clientUuid,
            name: formData.name,
            type: "client",
            clientType: formData.clientType,
          };
        }
        return option;
      });
    });

    // update selected attendees
    onChange((prev) => {
      return prev.map((option) => {
        if (option.uuid === selectedAttendeeForClientCreation.uuid) {
          return {
            ...option,
            uuid: clientUuid,
            name: formData.name,
            type: "client",
          };
        }
        return option;
      });
    });

    // close modal
    setIsAddClientFormOpen(false);

    // show toast
    toast.success("Client created", {
      autoClose: 2000,
    });
  };

  return (
    <>
      <Popover
        open={open}
        onOpenChange={(isOpen) => {
          // Clear the search term when the popover is closed, so that the next time it is opened it
          // will start fresh.
          if (!isOpen) {
            setSearchTerm("");
          }
          setOpen(isOpen);
        }}
        modal={modal}
      >
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              "w-full justify-between rounded-2xl p-3 text-base",
              "h-fit min-h-fit",
              triggerClassName
            )}
            aria-describedby={ariaDescribedBy}
            onClick={() => setOpen(!open)}
          >
            {leftIcon}
            {(() => {
              if (selected.length === 0) {
                return (
                  <Typography
                    className="inline-flex grow"
                    color="secondary"
                    asChild
                  >
                    <span>{placeholder}</span>
                  </Typography>
                );
              }
              return (
                <span className="inline-flex min-w-0 grow flex-wrap gap-2">
                  {selected.map((item) => {
                    const selectedOption = options.find(
                      ({ uuid }) => uuid === item.uuid
                    );
                    if (!selectedOption) return null;
                    return (
                      <AttendeeOptionTag
                        key={selectedOption.uuid} // Ensure each child has a unique key
                        attendeeOption={selectedOption}
                        onDelete={() =>
                          onChange(selected.filter((i) => i !== item))
                        }
                        onClick={(e) => {
                          // check for flag
                          if (!isClientCreationEnabled) {
                            return;
                          }

                          // continue usual flow for clients and users
                          if (
                            selectedOption.type &&
                            ["client", "user"].includes(selectedOption.type)
                          ) {
                            return;
                          }

                          openClientCreationFlow(selectedOption);
                          e.stopPropagation();
                        }}
                      />
                    );
                  })}
                </span>
              );
            })()}

            <CaretSortIcon className="h-6 w-6 shrink-0 self-baseline" />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className={cn(
            "w-fit min-w-[var(--radix-popper-anchor-width)] p-0",
            commandClassName
          )}
        >
          <Input
            ref={searchInputRef}
            placeholder="Search..."
            className="border-none bg-transparent shadow-none hover:bg-transparent has-[input:focus]:ring-0"
            type="search"
            leftIcon={<Search className="!h-5 !w-5" />}
            onChange={(event) => setSearchTerm(event.currentTarget.value)}
            onKeyDown={(event) => {
              const item = filteredOptions[cursor]?.item ?? null;
              switch (event.key) {
                case "Enter": {
                  if (item) {
                    const didSelect = toggleOptionSelection(item);
                    if (didSelect) {
                      clearInputAndFocus();
                    }
                  } else {
                    addNewOption();
                    clearInputAndFocus();
                  }
                  setOpen(true);
                  return;
                }

                case "ArrowUp": {
                  event.preventDefault();
                  moveCursorUp();
                  return;
                }
                case "ArrowDown": {
                  event.preventDefault();
                  moveCursorDown();
                  return;
                }

                default: {
                  return;
                }
              }
            }}
          />
          <Separator />
          <div className="p-1">
            {options.length === 0 && searchTerm.trim().length === 0 ? (
              <div className="py-6 text-center text-sm font-normal text-secondary">
                {emptyLabel}
              </div>
            ) : totalItemCount > 0 ? (
              <FixedSizeList
                height={listHeight}
                width="100%"
                itemCount={totalItemCount}
                itemSize={itemSize}
              >
                {OptionRow}
              </FixedSizeList>
            ) : (
              <div className="py-6 text-center text-sm font-normal text-secondary">
                {emptyLabel}
              </div>
            )}
          </div>
        </PopoverContent>
      </Popover>

      {isAddClientFormOpen && (
        <CreateEditClientFlow
          onOpenChange={setIsAddClientFormOpen}
          onSuccess={onClientCreation}
          prefilledDetails={{ text: prefilledData }}
          onError={(msg: string) => {
            toast.error(msg, {
              autoClose: 2000,
            });
          }}
        />
      )}
    </>
  );
};
