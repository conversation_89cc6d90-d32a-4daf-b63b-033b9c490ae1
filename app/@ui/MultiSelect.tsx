import * as React from "react";
import { Badge } from "~/@shadcn/ui/badge";
import { Button } from "~/@shadcn/ui/button";
import {
  Command,
  CommandInput,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from "~/@shadcn/ui/command";
import { Popover, PopoverTrigger, PopoverContent } from "~/@shadcn/ui/popover";
import { cn } from "~/@shadcn/utils";
import { CheckIcon, CaretSortIcon } from "@radix-ui/react-icons";
import { X } from "lucide-react";
import { Typography } from "~/@ui/Typography";
import { OptionType } from "~/@ui/Combobox";

// Exports
type Props = {
  options: OptionType[];
  selected: string[];
  placeholder?: string;
  leftIcon?: React.ReactNode;
  onChange: (value: string[]) => void;
  commandClassName?: string;
  triggerClassName?: string;
  hideSearch?: boolean;
  useLabelForSearch?: boolean;
  modal?: boolean;
};
/**
 * Heavily modified version of the MultiSelect component from this PR: https://github.com/shadcn-ui/ui/issues/66
 */
export const MultiSelect = ({
  options,
  selected,
  placeholder = "Select an item",
  leftIcon,
  onChange,
  commandClassName,
  triggerClassName,
  hideSearch,
  useLabelForSearch,
  modal,
  ...props
}: Props) => {
  const [open, setOpen] = React.useState(false);

  return (
    <Popover open={open} onOpenChange={setOpen} modal={modal} {...props}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between rounded border-gray-300 p-2 text-base hover:border-gray-300 hover:bg-white",
            "h-fit min-h-fit",
            triggerClassName
          )}
          onClick={() => setOpen(!open)}
        >
          {leftIcon}
          {(() => {
            if (selected.length === 0) {
              return (
                <Typography
                  className="inline-flex grow"
                  color="secondary"
                  asChild
                >
                  <span>{placeholder}</span>
                </Typography>
              );
            }
            return (
              <span className="inline-flex grow flex-wrap gap-2">
                {selected.map((item) => {
                  const selectedOption = options.find(
                    ({ value }) => value === item
                  );
                  if (!selectedOption) return null;
                  return (
                    <Badge
                      className="max-w-40 gap-1 [&>svg]:shrink-0"
                      inline="inline"
                      variant="secondary"
                      key={item}
                    >
                      {selectedOption.leftIcon}
                      <span className="overflow-hidden text-ellipsis whitespace-nowrap">
                        {selectedOption.label}
                      </span>
                      <Button
                        className="h-5 w-5 [&>svg]:h-4 [&>svg]:w-4"
                        size="icon-xs"
                        variant="ghost"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          onChange(selected.filter((i) => i !== item));
                        }}
                        asChild
                      >
                        <span role="button">
                          <X />
                        </span>
                      </Button>
                    </Badge>
                  );
                })}
              </span>
            );
          })()}

          <CaretSortIcon className="h-6 w-6 shrink-0 self-baseline" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-fit min-w-[var(--radix-popper-anchor-width)] p-0">
        <Command
          className={commandClassName}
          {...(useLabelForSearch
            ? {
                filter: (value, search, keywords) => {
                  if (
                    keywords?.length &&
                    keywords[0]?.includes(search.toLowerCase())
                  )
                    return 1;
                  return 0;
                },
              }
            : {})}
        >
          {!hideSearch && <CommandInput placeholder="Search..." />}
          <CommandList>
            <CommandEmpty>No item found.</CommandEmpty>
            <CommandGroup className="max-h-64 overflow-auto">
              {options.map((option) => (
                <CommandItem
                  key={option.value}
                  className="gap-2 overflow-hidden text-ellipsis whitespace-nowrap"
                  value={option.value}
                  onMouseDown={(e) => e.stopPropagation()}
                  onSelect={(currentValue) => {
                    onChange(
                      selected.includes(currentValue)
                        ? selected.filter((item) => item !== currentValue)
                        : [...selected, currentValue]
                    );
                    setOpen(true);
                  }}
                  {...(useLabelForSearch
                    ? { keywords: [option.label.toLowerCase()] }
                    : {})}
                >
                  {option.leftIcon ?? <span className="h-4 w-4 shrink-0" />}
                  <span className="grow overflow-hidden text-ellipsis whitespace-nowrap text-start">
                    {option.label}
                  </span>
                  <CheckIcon
                    className={cn(
                      "ml-auto h-4 w-4 shrink-0",
                      selected.includes(option.value)
                        ? "opacity-100"
                        : "opacity-0"
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};
